# Shining Button Shader System

A comprehensive UI button shining shader system with 5 different animation types for Unity.

## Features

### 5 Animation Types:
1. **Linear** - Horizontal shine sweep from left to right
2. **Diagonal** - Diagonal shine sweep from bottom-left to top-right
3. **Radial** - Circular shine expanding from center outward
4. **Wave** - Wavy sine-based shine pattern
5. **Pulse** - Pulsing ring effect from center

### Customizable Properties:
- Shine color and intensity
- Animation speed and duration
- Shine width/thickness
- Trigger controls (manual, hover, click)
- Loop settings

## Setup Instructions

### 1. Shader Setup
The shader `UI/ShiningButton` is automatically created in your project. It's designed specifically for UI elements and includes proper stencil support for UI masking.

### 2. Material Creation
1. Create a new Material in your project
2. Set the shader to `UI/ShiningButton`
3. Configure the properties as desired:
   - **Shine Color**: Color of the shine effect
   - **Shine Intensity**: Brightness multiplier (0-5)
   - **Shine Speed**: Animation speed (0-10)
   - **Shine Width**: Thickness of the shine (0.01-1)
   - **Animation Type**: Choose from 5 types
   - **Animation Trigger**: Enable/disable animation (0-1)
   - **Animation Duration**: How long each cycle lasts

### 3. Button Setup
1. Create a UI Button in your scene
2. Add the `ShiningButtonController` script to the button
3. The script will automatically:
   - Create a material instance
   - Set up the shader keywords
   - Handle animation triggers

### 4. Controller Configuration
Configure the `ShiningButtonController` component:

```csharp
[Header("Animation Settings")]
public ShineAnimationType animationType = ShineAnimationType.Linear;
public bool playOnStart = false;
public bool loopAnimation = false;
public float animationDuration = 1f;
public float delayBetweenLoops = 2f;

[Header("Shine Properties")]
public Color shineColor = Color.white;
public float shineIntensity = 2f;
public float shineSpeed = 2f;
public float shineWidth = 0.2f;

[Header("Trigger Settings")]
public bool triggerOnHover = true;
public bool triggerOnClick = true;
```

## Usage Examples

### Basic Usage
```csharp
// Get the controller
ShiningButtonController controller = GetComponent<ShiningButtonController>();

// Play animation manually
controller.PlayShineAnimation();

// Stop animation
controller.StopShineAnimation();

// Change animation type
controller.SetAnimationType(ShiningButtonController.ShineAnimationType.Radial);

// Update shine properties
controller.SetShineProperties(Color.gold, 3f, 1.5f, 0.3f);
```

### Demo Scene
Use the `ShiningButtonDemo` script to create a demo scene with multiple buttons showcasing all animation types:

1. Create 5 UI buttons
2. Add `ShiningButtonController` to each
3. Add `ShiningButtonDemo` to a GameObject
4. Assign the buttons to the demo script
5. Create control UI elements (sliders, toggles, buttons) and assign them

### Keyboard Shortcuts (in demo)
- **1-5**: Switch animation types
- **Space**: Play all animations
- **Escape**: Stop all animations

## Technical Details

### Shader Keywords
The shader uses keywords for efficient animation type switching:
- `_ANIMATIONTYPE_LINEAR`
- `_ANIMATIONTYPE_DIAGONAL`
- `_ANIMATIONTYPE_RADIAL`
- `_ANIMATIONTYPE_WAVE`
- `_ANIMATIONTYPE_PULSE`

### Performance Considerations
- Each button gets its own material instance to avoid conflicts
- Shader properties are cached using PropertyToID for better performance
- Animation is controlled via shader parameters, not CPU-intensive operations

### UI Compatibility
- Full support for Unity's UI system
- Proper stencil buffer handling for UI masking
- Alpha clipping support
- Works with Canvas scaling

## Customization

### Adding New Animation Types
1. Add new keyword in shader Properties
2. Add corresponding #pragma multi_compile_local line
3. Create new animation function in shader
4. Add case in fragment shader
5. Update C# enum and controller logic

### Modifying Existing Animations
Edit the animation functions in the shader:
- `GetLinearShine()` - Linear sweep
- `GetDiagonalShine()` - Diagonal sweep
- `GetRadialShine()` - Radial expansion
- `GetWaveShine()` - Wave pattern
- `GetPulseShine()` - Pulse rings

## Troubleshooting

### Common Issues:
1. **Shader not found**: Ensure the shader file is in the project and compiled
2. **Animation not playing**: Check that Animation Trigger is set to 1
3. **Wrong animation type**: Verify the correct keyword is enabled
4. **Performance issues**: Reduce the number of animated buttons or lower the shine intensity

### Debug Tips:
- Use Frame Debugger to inspect shader keywords
- Check material properties in the Inspector
- Verify UI Canvas settings for proper rendering

## Files Included:
- `shiningshader.shader` - Main UI shader with 5 animation types
- `ShiningButtonController.cs` - Component for controlling animations
- `ShiningButtonDemo.cs` - Demo scene controller
- `ShiningButton_README.md` - This documentation

Enjoy creating beautiful shining button effects!
