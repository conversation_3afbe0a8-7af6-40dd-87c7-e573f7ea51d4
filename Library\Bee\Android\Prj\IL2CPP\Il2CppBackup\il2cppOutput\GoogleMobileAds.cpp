﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1, typename T2>
struct VirtualActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2>
struct InterfaceActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3>
struct InterfaceActionInvoker3
{
	typedef void (*Action)(void*, T1, T2, T3, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2, T3 p3)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

// System.Action`1<GoogleMobileAds.Api.AdError>
struct Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6;
// System.Action`1<GoogleMobileAds.Api.AdValue>
struct Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF;
// System.Action`1<GoogleMobileAds.Common.AppState>
struct Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79;
// System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>
struct Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499;
// System.Action`1<GoogleMobileAds.Api.InitializationStatus>
struct Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE;
// System.Action`1<GoogleMobileAds.Api.LoadAdError>
struct Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C;
// System.Action`1<System.Object>
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87;
// System.Action`1<GoogleMobileAds.Api.Reward>
struct Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC;
// System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580;
// System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t6D103D50D92EED8806671DABC525008F7D560A68;
// System.Action`2<System.Object,System.Object>
struct Action_2_t156C43F079E7E68155FCDCD12DC77DD11AEF7E3C;
// System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418;
// System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E;
// System.Threading.AsyncLocal`1<System.Globalization.CultureInfo>
struct AsyncLocal_1_t1D3339EA4C8650D2DEDDF9553E5C932B3DC2CCFD;
// System.Collections.Generic.Dictionary`2<System.String,System.String>
struct Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83;
// System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>
struct EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E;
// System.EventHandler`1<System.EventArgs>
struct EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF;
// System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>
struct EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50;
// System.EventHandler`1<System.Object>
struct EventHandler_1_tD8C4A5BE1F7C91B1A7E99AE87AFD2F5432C38746;
// System.EventHandler`1<GoogleMobileAds.Api.Reward>
struct EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC;
// System.Collections.Generic.HashSet`1<System.String>
struct HashSet_1_tEFC6605F7DE53F71946C33FD371E53C3100F2178;
// System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras>
struct List_1_t5E2EB19AA6B9CFA733A67F57AF74DCBE981C3376;
// System.Collections.Generic.List`1<System.String>
struct List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD;
// System.Char[]
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
// System.Delegate[]
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
// System.Type[]
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
// System.Action
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
// GoogleMobileAds.Api.AdError
struct AdError_tA3BF7518349885A73447524367693A096B3DD1AE;
// GoogleMobileAds.Common.AdErrorClientEventArgs
struct AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D;
// GoogleMobileAds.Api.AdRequest
struct AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D;
// GoogleMobileAds.Api.AdSize
struct AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158;
// GoogleMobileAds.Api.AdValue
struct AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD;
// GoogleMobileAds.Api.AppOpenAd
struct AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC;
// GoogleMobileAds.Api.BannerView
struct BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB;
// System.Reflection.Binder
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
// System.Globalization.CultureInfo
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0;
// System.Delegate
struct Delegate_t;
// System.DelegateData
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
// System.EventArgs
struct EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377;
// System.Threading.ExecutionContext
struct ExecutionContext_t9D6EDFD92F0B2D391751963E2D77A8B03CB81710;
// GoogleMobileAds.Common.IAdErrorClient
struct IAdErrorClient_t1070A9DCB8BBE68B67D9ECBC19949CDF415AD2BF;
// GoogleMobileAds.Common.IAppOpenAdClient
struct IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1;
// GoogleMobileAds.Common.IAppStateEventClient
struct IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73;
// GoogleMobileAds.Common.IBannerClient
struct IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D;
// GoogleMobileAds.IClientFactory
struct IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252;
// GoogleMobileAds.Common.IInitializationStatusClient
struct IInitializationStatusClient_t8FB5AA028F4FF61A5B8952A9D5546BC331877A9D;
// GoogleMobileAds.Common.IInterstitialClient
struct IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784;
// GoogleMobileAds.Common.ILoadAdErrorClient
struct ILoadAdErrorClient_t82CFD9A1EFB235DD188F81112DF3EAE96806A8D9;
// GoogleMobileAds.Common.IMobileAdsClient
struct IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F;
// System.Security.Principal.IPrincipal
struct IPrincipal_tE7AF5096287F6C3472585E124CB38FF2A51EAB5F;
// GoogleMobileAds.Common.IResponseInfoClient
struct IResponseInfoClient_tCF770586616698786A19AB53CA4009AF567121B7;
// GoogleMobileAds.Common.IRewardedAdClient
struct IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE;
// GoogleMobileAds.Common.IRewardedInterstitialAdClient
struct IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5;
// GoogleMobileAds.Api.InitializationStatus
struct InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD;
// System.Threading.InternalThread
struct InternalThread_tF40B7BFCBD60C82BD8475A22FF5186CA10293687;
// GoogleMobileAds.Api.InterstitialAd
struct InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC;
// GoogleMobileAds.Api.LoadAdError
struct LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62;
// GoogleMobileAds.Common.LoadAdErrorClientEventArgs
struct LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2;
// System.LocalDataStoreHolder
struct LocalDataStoreHolder_t789DD474AE5141213C2105CE57830ECFC2D3C03F;
// System.LocalDataStoreMgr
struct LocalDataStoreMgr_t205F1783D5CC2B148E829B5882E5406FF9A3AC1E;
// GoogleMobileAds.Api.MaxAdContentRating
struct MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17;
// System.Reflection.MemberFilter
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
// System.Reflection.MethodInfo
struct MethodInfo_t;
// GoogleMobileAds.Api.MobileAds
struct MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5;
// System.MulticastDelegate
struct MulticastDelegate_t;
// GoogleMobileAds.Api.RequestConfiguration
struct RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB;
// GoogleMobileAds.Api.ResponseInfo
struct ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0;
// GoogleMobileAds.Api.Reward
struct Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511;
// GoogleMobileAds.Api.RewardedAd
struct RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09;
// GoogleMobileAds.Api.RewardedInterstitialAd
struct RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE;
// System.Threading.SendOrPostCallback
struct SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E;
// System.String
struct String_t;
// System.Threading.SynchronizationContext
struct SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0;
// System.Threading.Thread
struct Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F;
// System.Type
struct Type_t;
// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC;
// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866;
// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954;
// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0
struct U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159;
// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1
struct U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0;
// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6;
// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15;
// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD;
// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0
struct U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E;
// GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7
struct U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653;
// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA;
// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0;
// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E;
// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C;
// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF;
// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652;
// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87;
// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD;
// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA;
// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A;
// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2
struct U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73;
// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1
struct U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386;
// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88;
// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9;

IL2CPP_EXTERN_C RuntimeClass* Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IAdErrorClient_t1070A9DCB8BBE68B67D9ECBC19949CDF415AD2BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral1E40C18D8AC4A6D9A62D3A099D09AF791342A6DB;
IL2CPP_EXTERN_C String_t* _stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD;
IL2CPP_EXTERN_C String_t* _stringLiteralB0496525135724A6D7138AB94BD6661514AD7DAE;
IL2CPP_EXTERN_C String_t* _stringLiteralBDD22D9991854F9B5ADA35715330812319A1362B;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Type_GetType_m71A077E0B5DA3BD1DC0AB9AE387056CFCF56F93F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// <Module>
struct U3CModuleU3E_tE42916054522E4DD6B63B142419C43597A882300 
{
};

// GoogleMobileAds.Api.AdError
struct AdError_tA3BF7518349885A73447524367693A096B3DD1AE  : public RuntimeObject
{
	// GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Api.AdError::_client
	RuntimeObject* ____client_0;
};

// GoogleMobileAds.Api.AdRequest
struct AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D  : public RuntimeObject
{
	// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.AdRequest::CustomTargeting
	Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83* ___CustomTargeting_1;
	// System.Collections.Generic.HashSet`1<System.String> GoogleMobileAds.Api.AdRequest::Keywords
	HashSet_1_tEFC6605F7DE53F71946C33FD371E53C3100F2178* ___Keywords_2;
	// System.Collections.Generic.Dictionary`2<System.String,System.String> GoogleMobileAds.Api.AdRequest::Extras
	Dictionary_2_t46B2DB028096FA2B828359E52F37F3105A83AD83* ___Extras_3;
	// System.Collections.Generic.List`1<GoogleMobileAds.Api.Mediation.MediationExtras> GoogleMobileAds.Api.AdRequest::MediationExtras
	List_1_t5E2EB19AA6B9CFA733A67F57AF74DCBE981C3376* ___MediationExtras_4;
};

// GoogleMobileAds.Api.AppOpenAd
struct AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC  : public RuntimeObject
{
	// System.Action`1<GoogleMobileAds.Api.AdValue> GoogleMobileAds.Api.AppOpenAd::OnAdPaid
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___OnAdPaid_0;
	// System.Action GoogleMobileAds.Api.AppOpenAd::OnAdClicked
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdClicked_1;
	// System.Action GoogleMobileAds.Api.AppOpenAd::OnAdImpressionRecorded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdImpressionRecorded_2;
	// System.Action GoogleMobileAds.Api.AppOpenAd::OnAdFullScreenContentOpened
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentOpened_3;
	// System.Action GoogleMobileAds.Api.AppOpenAd::OnAdFullScreenContentClosed
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentClosed_4;
	// System.Action`1<GoogleMobileAds.Api.AdError> GoogleMobileAds.Api.AppOpenAd::OnAdFullScreenContentFailed
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___OnAdFullScreenContentFailed_5;
	// GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.Api.AppOpenAd::_client
	RuntimeObject* ____client_6;
	// System.Boolean GoogleMobileAds.Api.AppOpenAd::_canShowAd
	bool ____canShowAd_7;
};

// GoogleMobileAds.Api.AppStateEventNotifier
struct AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE  : public RuntimeObject
{
};

// GoogleMobileAds.Api.BannerView
struct BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB  : public RuntimeObject
{
	// System.Action GoogleMobileAds.Api.BannerView::OnBannerAdLoaded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnBannerAdLoaded_0;
	// System.Action`1<GoogleMobileAds.Api.LoadAdError> GoogleMobileAds.Api.BannerView::OnBannerAdLoadFailed
	Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* ___OnBannerAdLoadFailed_1;
	// System.Action`1<GoogleMobileAds.Api.AdValue> GoogleMobileAds.Api.BannerView::OnAdPaid
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___OnAdPaid_2;
	// System.Action GoogleMobileAds.Api.BannerView::OnAdClicked
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdClicked_3;
	// System.Action GoogleMobileAds.Api.BannerView::OnAdImpressionRecorded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdImpressionRecorded_4;
	// System.Action GoogleMobileAds.Api.BannerView::OnAdFullScreenContentOpened
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentOpened_5;
	// System.Action GoogleMobileAds.Api.BannerView::OnAdFullScreenContentClosed
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentClosed_6;
	// GoogleMobileAds.Common.IBannerClient GoogleMobileAds.Api.BannerView::_client
	RuntimeObject* ____client_7;
};

// System.Runtime.ConstrainedExecution.CriticalFinalizerObject
struct CriticalFinalizerObject_t1DCAB623CAEA6529A96F5F3EDE3C7048A6E313C9  : public RuntimeObject
{
};

// System.EventArgs
struct EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377  : public RuntimeObject
{
};

// GoogleMobileAds.Api.InitializationStatus
struct InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD  : public RuntimeObject
{
	// GoogleMobileAds.Common.IInitializationStatusClient GoogleMobileAds.Api.InitializationStatus::client
	RuntimeObject* ___client_0;
};

// GoogleMobileAds.Api.InterstitialAd
struct InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC  : public RuntimeObject
{
	// System.Action`1<GoogleMobileAds.Api.AdValue> GoogleMobileAds.Api.InterstitialAd::OnAdPaid
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___OnAdPaid_0;
	// System.Action GoogleMobileAds.Api.InterstitialAd::OnAdClicked
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdClicked_1;
	// System.Action GoogleMobileAds.Api.InterstitialAd::OnAdImpressionRecorded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdImpressionRecorded_2;
	// System.Action GoogleMobileAds.Api.InterstitialAd::OnAdFullScreenContentOpened
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentOpened_3;
	// System.Action GoogleMobileAds.Api.InterstitialAd::OnAdFullScreenContentClosed
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentClosed_4;
	// System.Action`1<GoogleMobileAds.Api.AdError> GoogleMobileAds.Api.InterstitialAd::OnAdFullScreenContentFailed
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___OnAdFullScreenContentFailed_5;
	// GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.Api.InterstitialAd::_client
	RuntimeObject* ____client_6;
	// System.Boolean GoogleMobileAds.Api.InterstitialAd::_canShowAd
	bool ____canShowAd_7;
};

// System.Reflection.MemberInfo
struct MemberInfo_t  : public RuntimeObject
{
};

// GoogleMobileAds.Api.MobileAds
struct MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5  : public RuntimeObject
{
	// GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.Api.MobileAds::client
	RuntimeObject* ___client_0;
};

// GoogleMobileAds.Api.ResponseInfo
struct ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0  : public RuntimeObject
{
	// GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Api.ResponseInfo::_client
	RuntimeObject* ____client_0;
};

// GoogleMobileAds.Api.RewardedAd
struct RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09  : public RuntimeObject
{
	// System.Action`1<GoogleMobileAds.Api.AdValue> GoogleMobileAds.Api.RewardedAd::OnAdPaid
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___OnAdPaid_0;
	// System.Action GoogleMobileAds.Api.RewardedAd::OnAdClicked
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdClicked_1;
	// System.Action GoogleMobileAds.Api.RewardedAd::OnAdImpressionRecorded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdImpressionRecorded_2;
	// System.Action GoogleMobileAds.Api.RewardedAd::OnAdFullScreenContentOpened
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentOpened_3;
	// System.Action GoogleMobileAds.Api.RewardedAd::OnAdFullScreenContentClosed
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentClosed_4;
	// System.Action`1<GoogleMobileAds.Api.AdError> GoogleMobileAds.Api.RewardedAd::OnAdFullScreenContentFailed
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___OnAdFullScreenContentFailed_5;
	// GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.Api.RewardedAd::_client
	RuntimeObject* ____client_6;
	// System.Boolean GoogleMobileAds.Api.RewardedAd::_canShowAd
	bool ____canShowAd_7;
	// System.Action`1<GoogleMobileAds.Api.Reward> GoogleMobileAds.Api.RewardedAd::_userRewardEarnedCallback
	Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* ____userRewardEarnedCallback_8;
};

// GoogleMobileAds.Api.RewardedInterstitialAd
struct RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE  : public RuntimeObject
{
	// System.Action`1<GoogleMobileAds.Api.AdValue> GoogleMobileAds.Api.RewardedInterstitialAd::OnAdPaid
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___OnAdPaid_0;
	// System.Action GoogleMobileAds.Api.RewardedInterstitialAd::OnAdClicked
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdClicked_1;
	// System.Action GoogleMobileAds.Api.RewardedInterstitialAd::OnAdImpressionRecorded
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdImpressionRecorded_2;
	// System.Action GoogleMobileAds.Api.RewardedInterstitialAd::OnAdFullScreenContentOpened
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentOpened_3;
	// System.Action GoogleMobileAds.Api.RewardedInterstitialAd::OnAdFullScreenContentClosed
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnAdFullScreenContentClosed_4;
	// System.Action`1<GoogleMobileAds.Api.AdError> GoogleMobileAds.Api.RewardedInterstitialAd::OnAdFullScreenContentFailed
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___OnAdFullScreenContentFailed_5;
	// GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.Api.RewardedInterstitialAd::_client
	RuntimeObject* ____client_6;
	// System.Boolean GoogleMobileAds.Api.RewardedInterstitialAd::_canShowAd
	bool ____canShowAd_7;
	// System.Action`1<GoogleMobileAds.Api.Reward> GoogleMobileAds.Api.RewardedInterstitialAd::_userRewardEarnedCallback
	Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* ____userRewardEarnedCallback_8;
};

// System.String
struct String_t  : public RuntimeObject
{
	// System.Int32 System.String::_stringLength
	int32_t ____stringLength_4;
	// System.Char System.String::_firstChar
	Il2CppChar ____firstChar_5;
};

// System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC  : public RuntimeObject
{
	// System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError> GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::adLoadCallback
	Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* ___adLoadCallback_0;
	// GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::client
	RuntimeObject* ___client_1;
};

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdError GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::adError
	AdError_tA3BF7518349885A73447524367693A096B3DD1AE* ___adError_0;
	// GoogleMobileAds.Api.AppOpenAd GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::$this
	AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* ___U24this_1;
};

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::adValue
	AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___adValue_0;
	// GoogleMobileAds.Api.AppOpenAd GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::$this
	AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* ___U24this_1;
};

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0
struct U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159  : public RuntimeObject
{
	// GoogleMobileAds.Api.LoadAdError GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::loadAdError
	LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___loadAdError_0;
	// GoogleMobileAds.Api.BannerView GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::$this
	BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* ___U24this_1;
};

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1
struct U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::adValue
	AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___adValue_0;
	// GoogleMobileAds.Api.BannerView GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::$this
	BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* ___U24this_1;
};

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6  : public RuntimeObject
{
	// GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::client
	RuntimeObject* ___client_0;
	// System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError> GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::adLoadCallback
	Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* ___adLoadCallback_1;
};

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdError GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::adError
	AdError_tA3BF7518349885A73447524367693A096B3DD1AE* ___adError_0;
	// GoogleMobileAds.Api.InterstitialAd GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::$this
	InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* ___U24this_1;
};

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::adValue
	AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___adValue_0;
	// GoogleMobileAds.Api.InterstitialAd GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::$this
	InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* ___U24this_1;
};

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0
struct U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E  : public RuntimeObject
{
	// System.Action`1<GoogleMobileAds.Api.InitializationStatus> GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::initCompleteAction
	Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* ___initCompleteAction_0;
};

// GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7
struct U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653  : public RuntimeObject
{
	// System.Action GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::action
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___action_0;
};

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA  : public RuntimeObject
{
	// System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError> GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::adLoadCallback
	Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* ___adLoadCallback_0;
	// GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::client
	RuntimeObject* ___client_1;
};

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdError GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::adError
	AdError_tA3BF7518349885A73447524367693A096B3DD1AE* ___adError_0;
	// GoogleMobileAds.Api.RewardedAd GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::$this
	RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* ___U24this_1;
};

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::adValue
	AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___adValue_0;
	// GoogleMobileAds.Api.RewardedAd GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::$this
	RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* ___U24this_1;
};

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C  : public RuntimeObject
{
	// GoogleMobileAds.Api.Reward GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::args
	Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* ___args_0;
	// GoogleMobileAds.Api.RewardedAd GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::$this
	RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* ___U24this_1;
};

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0
struct U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF  : public RuntimeObject
{
	// System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError> GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::adLoadCallback
	Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* ___adLoadCallback_0;
	// GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::client
	RuntimeObject* ___client_1;
};

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2
struct U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdError GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::adError
	AdError_tA3BF7518349885A73447524367693A096B3DD1AE* ___adError_0;
	// GoogleMobileAds.Api.RewardedInterstitialAd GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::$this
	RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* ___U24this_1;
};

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3
struct U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::adValue
	AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___adValue_0;
	// GoogleMobileAds.Api.RewardedInterstitialAd GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::$this
	RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* ___U24this_1;
};

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4
struct U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD  : public RuntimeObject
{
	// GoogleMobileAds.Api.Reward GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::args
	Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* ___args_0;
	// GoogleMobileAds.Api.RewardedInterstitialAd GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::$this
	RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* ___U24this_1;
};

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA  : public RuntimeObject
{
	// GoogleMobileAds.Api.LoadAdError GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::loadAdError
	LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___loadAdError_0;
	// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0 GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* ___U3CU3Ef__refU240_1;
};

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A  : public RuntimeObject
{
	// GoogleMobileAds.Api.InterstitialAd GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::interstitialAd
	InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* ___interstitialAd_0;
	// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0 GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* ___U3CU3Ef__refU240_1;
};

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2
struct U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73  : public RuntimeObject
{
	// GoogleMobileAds.Api.LoadAdError GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::loadAdError
	LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___loadAdError_0;
	// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0 GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* ___U3CU3Ef__refU240_1;
};

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1
struct U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386  : public RuntimeObject
{
	// GoogleMobileAds.Common.IInitializationStatusClient GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::initStatusClient
	RuntimeObject* ___initStatusClient_0;
	// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0 GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::<>f__ref$0
	U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* ___U3CU3Ef__refU240_1;
};

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88  : public RuntimeObject
{
	// GoogleMobileAds.Api.LoadAdError GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::loadAdError
	LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___loadAdError_0;
	// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0 GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* ___U3CU3Ef__refU240_1;
};

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1
struct U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9  : public RuntimeObject
{
	// GoogleMobileAds.Api.LoadAdError GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::loadAdError
	LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___loadAdError_0;
	// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0 GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>f__ref$0
	U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* ___U3CU3Ef__refU240_1;
};

// System.Nullable`1<System.Boolean>
struct Nullable_1_t78F453FADB4A9F50F267A4E349019C34410D1A01 
{
	// System.Boolean System.Nullable`1::hasValue
	bool ___hasValue_0;
	// T System.Nullable`1::value
	bool ___value_1;
};

// GoogleMobileAds.Common.AdErrorClientEventArgs
struct AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	// GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.AdErrorClientEventArgs::<AdErrorClient>k__BackingField
	RuntimeObject* ___U3CAdErrorClientU3Ek__BackingField_1;
};

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;
};

// System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
// Native definition for P/Invoke marshalling of System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.Enum
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};

// System.Int32
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;
};

// System.IntPtr
struct IntPtr_t 
{
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;
};

// GoogleMobileAds.Api.LoadAdError
struct LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62  : public AdError_tA3BF7518349885A73447524367693A096B3DD1AE
{
	// GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Api.LoadAdError::_client
	RuntimeObject* ____client_1;
};

// GoogleMobileAds.Common.LoadAdErrorClientEventArgs
struct LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	// GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Common.LoadAdErrorClientEventArgs::<LoadAdErrorClient>k__BackingField
	RuntimeObject* ___U3CLoadAdErrorClientU3Ek__BackingField_1;
};

// GoogleMobileAds.Api.Reward
struct Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511  : public EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377
{
	// System.String GoogleMobileAds.Api.Reward::Type
	String_t* ___Type_1;
	// System.Double GoogleMobileAds.Api.Reward::Amount
	double ___Amount_2;
};

// System.Single
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	// System.Single System.Single::m_value
	float ___m_value_0;
};

// System.Threading.Thread
struct Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F  : public CriticalFinalizerObject_t1DCAB623CAEA6529A96F5F3EDE3C7048A6E313C9
{
	// System.Threading.InternalThread System.Threading.Thread::internal_thread
	InternalThread_tF40B7BFCBD60C82BD8475A22FF5186CA10293687* ___internal_thread_6;
	// System.Object System.Threading.Thread::m_ThreadStartArg
	RuntimeObject* ___m_ThreadStartArg_7;
	// System.Object System.Threading.Thread::pending_exception
	RuntimeObject* ___pending_exception_8;
	// System.MulticastDelegate System.Threading.Thread::m_Delegate
	MulticastDelegate_t* ___m_Delegate_10;
	// System.Threading.ExecutionContext System.Threading.Thread::m_ExecutionContext
	ExecutionContext_t9D6EDFD92F0B2D391751963E2D77A8B03CB81710* ___m_ExecutionContext_11;
	// System.Boolean System.Threading.Thread::m_ExecutionContextBelongsToOuterScope
	bool ___m_ExecutionContextBelongsToOuterScope_12;
	// System.Security.Principal.IPrincipal System.Threading.Thread::principal
	RuntimeObject* ___principal_13;
	// System.Int32 System.Threading.Thread::principal_version
	int32_t ___principal_version_14;
};

// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};

// GoogleMobileAds.Api.AdPosition
struct AdPosition_tC5663BBEC4D9F57BB911ABB0349BE51F0747D1A3 
{
	// System.Int32 GoogleMobileAds.Api.AdPosition::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Common.AppState
struct AppState_tBC2F72F60F51FE0CB94604F23B86874654E551A5 
{
	// System.Int32 GoogleMobileAds.Common.AppState::value__
	int32_t ___value___2;
};

// System.Delegate
struct Delegate_t  : public RuntimeObject
{
	// System.IntPtr System.Delegate::method_ptr
	intptr_t ___method_ptr_0;
	// System.IntPtr System.Delegate::invoke_impl
	intptr_t ___invoke_impl_1;
	// System.Object System.Delegate::m_target
	RuntimeObject* ___m_target_2;
	// System.IntPtr System.Delegate::method
	intptr_t ___method_3;
	// System.IntPtr System.Delegate::delegate_trampoline
	intptr_t ___delegate_trampoline_4;
	// System.IntPtr System.Delegate::extra_arg
	intptr_t ___extra_arg_5;
	// System.IntPtr System.Delegate::method_code
	intptr_t ___method_code_6;
	// System.IntPtr System.Delegate::interp_method
	intptr_t ___interp_method_7;
	// System.IntPtr System.Delegate::interp_invoke_impl
	intptr_t ___interp_invoke_impl_8;
	// System.Reflection.MethodInfo System.Delegate::method_info
	MethodInfo_t* ___method_info_9;
	// System.Reflection.MethodInfo System.Delegate::original_method_info
	MethodInfo_t* ___original_method_info_10;
	// System.DelegateData System.Delegate::data
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data_11;
	// System.Boolean System.Delegate::method_is_virtual
	bool ___method_is_virtual_12;
};
// Native definition for P/Invoke marshalling of System.Delegate
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	intptr_t ___interp_method_7;
	intptr_t ___interp_invoke_impl_8;
	MethodInfo_t* ___method_info_9;
	MethodInfo_t* ___original_method_info_10;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data_11;
	int32_t ___method_is_virtual_12;
};
// Native definition for COM marshalling of System.Delegate
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr_0;
	intptr_t ___invoke_impl_1;
	Il2CppIUnknown* ___m_target_2;
	intptr_t ___method_3;
	intptr_t ___delegate_trampoline_4;
	intptr_t ___extra_arg_5;
	intptr_t ___method_code_6;
	intptr_t ___interp_method_7;
	intptr_t ___interp_invoke_impl_8;
	MethodInfo_t* ___method_info_9;
	MethodInfo_t* ___original_method_info_10;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data_11;
	int32_t ___method_is_virtual_12;
};

// GoogleMobileAds.Api.Orientation
struct Orientation_tA42A910C028412D24B526CAEFD58B00C55242CB0 
{
	// System.Int32 GoogleMobileAds.Api.Orientation::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Api.PublisherPrivacyPersonalizationState
struct PublisherPrivacyPersonalizationState_t1C90D3EAA372240D9EA95816905BABBEEBA1272D 
{
	// System.Int32 GoogleMobileAds.Api.PublisherPrivacyPersonalizationState::value__
	int32_t ___value___2;
};

// UnityEngine.RuntimePlatform
struct RuntimePlatform_t9A8AAF204603076FCAAECCCC05DA386AEE7BF66E 
{
	// System.Int32 UnityEngine.RuntimePlatform::value__
	int32_t ___value___2;
};

// System.RuntimeTypeHandle
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	// System.IntPtr System.RuntimeTypeHandle::value
	intptr_t ___value_0;
};

// System.Threading.SynchronizationContextProperties
struct SynchronizationContextProperties_t5ED82C778B4C396AD94A93CFBEF00022BDECF058 
{
	// System.Int32 System.Threading.SynchronizationContextProperties::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Api.TagForChildDirectedTreatment
struct TagForChildDirectedTreatment_t477FA75E864BC33863535EEE486F6D05D1343C80 
{
	// System.Int32 GoogleMobileAds.Api.TagForChildDirectedTreatment::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Api.TagForUnderAgeOfConsent
struct TagForUnderAgeOfConsent_t2A2F4F1D84039FADF9022AA6E92E9893E60B01ED 
{
	// System.Int32 GoogleMobileAds.Api.TagForUnderAgeOfConsent::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Api.AdSize/Type
struct Type_tB4F3FFEF78203230302E9D3C0763C92B3C5EEE20 
{
	// System.Int32 GoogleMobileAds.Api.AdSize/Type::value__
	int32_t ___value___2;
};

// GoogleMobileAds.Api.AdValue/PrecisionType
struct PrecisionType_t5B0FBF4C3E44BA119E289EF000BEFD5EA7EA471C 
{
	// System.Int32 GoogleMobileAds.Api.AdValue/PrecisionType::value__
	int32_t ___value___2;
};

// System.Nullable`1<GoogleMobileAds.Api.PublisherPrivacyPersonalizationState>
struct Nullable_1_t8E5F48677B7FA223E824975C2D25CFDAD10B73AF 
{
	// System.Boolean System.Nullable`1::hasValue
	bool ___hasValue_0;
	// T System.Nullable`1::value
	int32_t ___value_1;
};

// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment>
struct Nullable_1_t5C1ED9E759C25CA8D8C3AD08588837283BCD0ADD 
{
	// System.Boolean System.Nullable`1::hasValue
	bool ___hasValue_0;
	// T System.Nullable`1::value
	int32_t ___value_1;
};

// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent>
struct Nullable_1_tD9EF87DE17BC8563283D8BCB64EF209BCFE74FD3 
{
	// System.Boolean System.Nullable`1::hasValue
	bool ___hasValue_0;
	// T System.Nullable`1::value
	int32_t ___value_1;
};

// GoogleMobileAds.Api.AdSize
struct AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdSize/Type GoogleMobileAds.Api.AdSize::_type
	int32_t ____type_0;
	// GoogleMobileAds.Api.Orientation GoogleMobileAds.Api.AdSize::_orientation
	int32_t ____orientation_1;
	// System.Int32 GoogleMobileAds.Api.AdSize::_width
	int32_t ____width_2;
	// System.Int32 GoogleMobileAds.Api.AdSize::_height
	int32_t ____height_3;
};

// GoogleMobileAds.Api.AdValue
struct AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD  : public RuntimeObject
{
	// GoogleMobileAds.Api.AdValue/PrecisionType GoogleMobileAds.Api.AdValue::<Precision>k__BackingField
	int32_t ___U3CPrecisionU3Ek__BackingField_0;
	// System.Int64 GoogleMobileAds.Api.AdValue::<Value>k__BackingField
	int64_t ___U3CValueU3Ek__BackingField_1;
	// System.String GoogleMobileAds.Api.AdValue::<CurrencyCode>k__BackingField
	String_t* ___U3CCurrencyCodeU3Ek__BackingField_2;
};

// System.MulticastDelegate
struct MulticastDelegate_t  : public Delegate_t
{
	// System.Delegate[] System.MulticastDelegate::delegates
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates_13;
};
// Native definition for P/Invoke marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates_13;
};
// Native definition for COM marshalling of System.MulticastDelegate
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates_13;
};

// System.Threading.SynchronizationContext
struct SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0  : public RuntimeObject
{
	// System.Threading.SynchronizationContextProperties System.Threading.SynchronizationContext::_props
	int32_t ____props_0;
};

// System.Type
struct Type_t  : public MemberInfo_t
{
	// System.RuntimeTypeHandle System.Type::_impl
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl_8;
};

// System.Action`1<GoogleMobileAds.Api.AdError>
struct Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Api.AdValue>
struct Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Common.AppState>
struct Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>
struct Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Api.InitializationStatus>
struct Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Api.LoadAdError>
struct Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C  : public MulticastDelegate_t
{
};

// System.Action`1<System.Object>
struct Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87  : public MulticastDelegate_t
{
};

// System.Action`1<GoogleMobileAds.Api.Reward>
struct Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC  : public MulticastDelegate_t
{
};

// System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580  : public MulticastDelegate_t
{
};

// System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t6D103D50D92EED8806671DABC525008F7D560A68  : public MulticastDelegate_t
{
};

// System.Action`2<System.Object,System.Object>
struct Action_2_t156C43F079E7E68155FCDCD12DC77DD11AEF7E3C  : public MulticastDelegate_t
{
};

// System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418  : public MulticastDelegate_t
{
};

// System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>
struct Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E  : public MulticastDelegate_t
{
};

// System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>
struct EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E  : public MulticastDelegate_t
{
};

// System.EventHandler`1<System.EventArgs>
struct EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF  : public MulticastDelegate_t
{
};

// System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>
struct EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50  : public MulticastDelegate_t
{
};

// System.EventHandler`1<GoogleMobileAds.Api.Reward>
struct EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC  : public MulticastDelegate_t
{
};

// System.Action
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};

// GoogleMobileAds.Api.RequestConfiguration
struct RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB  : public RuntimeObject
{
	// GoogleMobileAds.Api.MaxAdContentRating GoogleMobileAds.Api.RequestConfiguration::MaxAdContentRating
	MaxAdContentRating_t8F4AA13668EB7FF7A5F6F369845B5280BBE0BF17* ___MaxAdContentRating_0;
	// System.Nullable`1<GoogleMobileAds.Api.TagForChildDirectedTreatment> GoogleMobileAds.Api.RequestConfiguration::TagForChildDirectedTreatment
	Nullable_1_t5C1ED9E759C25CA8D8C3AD08588837283BCD0ADD ___TagForChildDirectedTreatment_1;
	// System.Nullable`1<GoogleMobileAds.Api.TagForUnderAgeOfConsent> GoogleMobileAds.Api.RequestConfiguration::TagForUnderAgeOfConsent
	Nullable_1_tD9EF87DE17BC8563283D8BCB64EF209BCFE74FD3 ___TagForUnderAgeOfConsent_2;
	// System.Nullable`1<GoogleMobileAds.Api.PublisherPrivacyPersonalizationState> GoogleMobileAds.Api.RequestConfiguration::PublisherPrivacyPersonalizationState
	Nullable_1_t8E5F48677B7FA223E824975C2D25CFDAD10B73AF ___PublisherPrivacyPersonalizationState_3;
	// System.Collections.Generic.List`1<System.String> GoogleMobileAds.Api.RequestConfiguration::TestDeviceIds
	List_1_tF470A3BE5C1B5B68E1325EF3F109D172E60BD7CD* ___TestDeviceIds_4;
	// System.Nullable`1<System.Boolean> GoogleMobileAds.Api.RequestConfiguration::PublisherFirstPartyIdEnabled
	Nullable_1_t78F453FADB4A9F50F267A4E349019C34410D1A01 ___PublisherFirstPartyIdEnabled_5;
};

// System.Threading.SendOrPostCallback
struct SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E  : public MulticastDelegate_t
{
};

// <Module>

// <Module>

// GoogleMobileAds.Api.AdError

// GoogleMobileAds.Api.AdError

// GoogleMobileAds.Api.AdRequest
struct AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D_StaticFields
{
	// System.String GoogleMobileAds.Api.AdRequest::<Version>k__BackingField
	String_t* ___U3CVersionU3Ek__BackingField_0;
};

// GoogleMobileAds.Api.AdRequest

// GoogleMobileAds.Api.AppOpenAd

// GoogleMobileAds.Api.AppOpenAd

// GoogleMobileAds.Api.AppStateEventNotifier
struct AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_StaticFields
{
	// GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.Api.AppStateEventNotifier::client
	RuntimeObject* ___client_0;
};

// GoogleMobileAds.Api.AppStateEventNotifier

// GoogleMobileAds.Api.BannerView

// GoogleMobileAds.Api.BannerView

// System.EventArgs
struct EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377_StaticFields
{
	// System.EventArgs System.EventArgs::Empty
	EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___Empty_0;
};

// System.EventArgs

// GoogleMobileAds.Api.InitializationStatus

// GoogleMobileAds.Api.InitializationStatus

// GoogleMobileAds.Api.InterstitialAd

// GoogleMobileAds.Api.InterstitialAd

// GoogleMobileAds.Api.MobileAds
struct MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields
{
	// GoogleMobileAds.IClientFactory GoogleMobileAds.Api.MobileAds::clientFactory
	RuntimeObject* ___clientFactory_1;
	// System.Threading.SynchronizationContext GoogleMobileAds.Api.MobileAds::_synchronizationContext
	SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* ____synchronizationContext_2;
	// System.Int32 GoogleMobileAds.Api.MobileAds::_unityMainThreadId
	int32_t ____unityMainThreadId_3;
	// GoogleMobileAds.Api.MobileAds GoogleMobileAds.Api.MobileAds::instance
	MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* ___instance_4;
	// System.Boolean GoogleMobileAds.Api.MobileAds::<RaiseAdEventsOnUnityMainThread>k__BackingField
	bool ___U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField_5;
};

// GoogleMobileAds.Api.MobileAds

// GoogleMobileAds.Api.ResponseInfo

// GoogleMobileAds.Api.ResponseInfo

// GoogleMobileAds.Api.RewardedAd

// GoogleMobileAds.Api.RewardedAd

// GoogleMobileAds.Api.RewardedInterstitialAd

// GoogleMobileAds.Api.RewardedInterstitialAd

// System.String
struct String_t_StaticFields
{
	// System.String System.String::Empty
	String_t* ___Empty_6;
};

// System.String

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1

// GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0

// GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7

// GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2

// GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1

// GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1

// GoogleMobileAds.Common.AdErrorClientEventArgs

// GoogleMobileAds.Common.AdErrorClientEventArgs

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;
};

// System.Boolean

// System.Int32

// System.Int32

// System.IntPtr
struct IntPtr_t_StaticFields
{
	// System.IntPtr System.IntPtr::Zero
	intptr_t ___Zero_1;
};

// System.IntPtr

// GoogleMobileAds.Api.LoadAdError

// GoogleMobileAds.Api.LoadAdError

// GoogleMobileAds.Common.LoadAdErrorClientEventArgs

// GoogleMobileAds.Common.LoadAdErrorClientEventArgs

// GoogleMobileAds.Api.Reward

// GoogleMobileAds.Api.Reward

// System.Single

// System.Single

// System.Threading.Thread
struct Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F_StaticFields
{
	// System.LocalDataStoreMgr System.Threading.Thread::s_LocalDataStoreMgr
	LocalDataStoreMgr_t205F1783D5CC2B148E829B5882E5406FF9A3AC1E* ___s_LocalDataStoreMgr_0;
	// System.Threading.AsyncLocal`1<System.Globalization.CultureInfo> System.Threading.Thread::s_asyncLocalCurrentCulture
	AsyncLocal_1_t1D3339EA4C8650D2DEDDF9553E5C932B3DC2CCFD* ___s_asyncLocalCurrentCulture_4;
	// System.Threading.AsyncLocal`1<System.Globalization.CultureInfo> System.Threading.Thread::s_asyncLocalCurrentUICulture
	AsyncLocal_1_t1D3339EA4C8650D2DEDDF9553E5C932B3DC2CCFD* ___s_asyncLocalCurrentUICulture_5;
};

// System.Threading.Thread
struct Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F_ThreadStaticFields
{
	// System.LocalDataStoreHolder System.Threading.Thread::s_LocalDataStore
	LocalDataStoreHolder_t789DD474AE5141213C2105CE57830ECFC2D3C03F* ___s_LocalDataStore_1;
	// System.Globalization.CultureInfo System.Threading.Thread::m_CurrentCulture
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___m_CurrentCulture_2;
	// System.Globalization.CultureInfo System.Threading.Thread::m_CurrentUICulture
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___m_CurrentUICulture_3;
	// System.Threading.Thread System.Threading.Thread::current_thread
	Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F* ___current_thread_9;
};

// System.Void

// System.Void

// GoogleMobileAds.Api.AdPosition

// GoogleMobileAds.Api.AdPosition

// System.Delegate

// System.Delegate

// UnityEngine.RuntimePlatform

// UnityEngine.RuntimePlatform

// GoogleMobileAds.Api.AdSize
struct AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158_StaticFields
{
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::Banner
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___Banner_4;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::MediumRectangle
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___MediumRectangle_5;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::IABBanner
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___IABBanner_6;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::Leaderboard
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___Leaderboard_7;
	// GoogleMobileAds.Api.AdSize GoogleMobileAds.Api.AdSize::SmartBanner
	AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___SmartBanner_8;
	// System.Int32 GoogleMobileAds.Api.AdSize::FullWidth
	int32_t ___FullWidth_9;
};

// GoogleMobileAds.Api.AdSize

// GoogleMobileAds.Api.AdValue

// GoogleMobileAds.Api.AdValue

// System.Threading.SynchronizationContext

// System.Threading.SynchronizationContext

// System.Type
struct Type_t_StaticFields
{
	// System.Reflection.Binder modreq(System.Runtime.CompilerServices.IsVolatile) System.Type::s_defaultBinder
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder_0;
	// System.Char System.Type::Delimiter
	Il2CppChar ___Delimiter_1;
	// System.Type[] System.Type::EmptyTypes
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes_2;
	// System.Object System.Type::Missing
	RuntimeObject* ___Missing_3;
	// System.Reflection.MemberFilter System.Type::FilterAttribute
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute_4;
	// System.Reflection.MemberFilter System.Type::FilterName
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName_5;
	// System.Reflection.MemberFilter System.Type::FilterNameIgnoreCase
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase_6;
};

// System.Type

// System.Action`1<GoogleMobileAds.Api.AdError>

// System.Action`1<GoogleMobileAds.Api.AdError>

// System.Action`1<GoogleMobileAds.Api.AdValue>

// System.Action`1<GoogleMobileAds.Api.AdValue>

// System.Action`1<GoogleMobileAds.Common.AppState>

// System.Action`1<GoogleMobileAds.Common.AppState>

// System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>

// System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>

// System.Action`1<GoogleMobileAds.Api.InitializationStatus>

// System.Action`1<GoogleMobileAds.Api.InitializationStatus>

// System.Action`1<GoogleMobileAds.Api.LoadAdError>

// System.Action`1<GoogleMobileAds.Api.LoadAdError>

// System.Action`1<System.Object>

// System.Action`1<System.Object>

// System.Action`1<GoogleMobileAds.Api.Reward>

// System.Action`1<GoogleMobileAds.Api.Reward>

// System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<System.Object,System.Object>

// System.Action`2<System.Object,System.Object>

// System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>

// System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>

// System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>

// System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>

// System.EventHandler`1<System.EventArgs>

// System.EventHandler`1<System.EventArgs>

// System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>

// System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>

// System.EventHandler`1<GoogleMobileAds.Api.Reward>

// System.EventHandler`1<GoogleMobileAds.Api.Reward>

// System.Action

// System.Action

// GoogleMobileAds.Api.RequestConfiguration

// GoogleMobileAds.Api.RequestConfiguration

// System.Threading.SendOrPostCallback

// System.Threading.SendOrPostCallback
#ifdef __clang__
#pragma clang diagnostic pop
#endif


// System.Void System.EventHandler`1<System.Object>::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventHandler_1__ctor_mBA2E748CFBA7A0EEC0A373A6F634D3B5774EA370_gshared (EventHandler_1_tD8C4A5BE1F7C91B1A7E99AE87AFD2F5432C38746* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
// System.Void System.Action`1<System.Object>::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
// System.Void System.Action`2<System.Object,System.Object>::Invoke(T1,T2)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline (Action_2_t156C43F079E7E68155FCDCD12DC77DD11AEF7E3C* __this, RuntimeObject* ___0_arg1, RuntimeObject* ___1_arg2, const RuntimeMethod* method) ;
// System.Void System.Action`1<System.Object>::Invoke(T)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;

// System.Void System.Object::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_RegisterAdEvents_m942A3CA51674726CDD0C3F6A9BDA83C0EF721B53 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) ;
// System.Delegate System.Delegate::Combine(System.Delegate,System.Delegate)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
// System.Delegate System.Delegate::Remove(System.Delegate,System.Delegate)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_mC3285F87E7CF1CDD7E8646879D08F71323457FFB (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* __this, const RuntimeMethod* method) ;
// System.Void UnityEngine.Debug::LogError(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
// GoogleMobileAds.IClientFactory GoogleMobileAds.Api.MobileAds::GetClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E (const RuntimeMethod* method) ;
// System.Void System.EventHandler`1<System.EventArgs>::.ctor(System.Object,System.IntPtr)
inline void EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0 (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*, RuntimeObject*, intptr_t, const RuntimeMethod*))EventHandler_1__ctor_mBA2E748CFBA7A0EEC0A373A6F634D3B5774EA370_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Void System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>::.ctor(System.Object,System.IntPtr)
inline void EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9 (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*, RuntimeObject*, intptr_t, const RuntimeMethod*))EventHandler_1__ctor_mBA2E748CFBA7A0EEC0A373A6F634D3B5774EA370_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Boolean GoogleMobileAds.Api.AppOpenAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AppOpenAd_CanShowAd_m822270A031DF79CB0ED3AD52240BC4100F0DFAB1 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) ;
// System.Void System.Action::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
// System.Void System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>::.ctor(System.Object,System.IntPtr)
inline void EventHandler_1__ctor_mEB4BDC01DF31AFC470842A776CABEA9053594C64 (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E*, RuntimeObject*, intptr_t, const RuntimeMethod*))EventHandler_1__ctor_mBA2E748CFBA7A0EEC0A373A6F634D3B5774EA370_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Void System.Action`1<GoogleMobileAds.Api.AdValue>::.ctor(System.Object,System.IntPtr)
inline void Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9 (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Void GoogleMobileAds.Api.MobileAds::RaiseAction(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m960091194D004208900F150D954F8985848871B1 (U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* __this, const RuntimeMethod* method) ;
// GoogleMobileAds.Common.IAdErrorClient GoogleMobileAds.Common.AdErrorClientEventArgs::get_AdErrorClient()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline (AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AdError::.ctor(GoogleMobileAds.Common.IAdErrorClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07 (AdError_tA3BF7518349885A73447524367693A096B3DD1AE* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m05E9BE4D156BBC16C191C438B22867C2C7142317 (U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* __this, const RuntimeMethod* method) ;
// System.Void System.Action::Invoke()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m536EDFA1E591F37667D32F2BB8F2CDA45711279A (U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* __this, const RuntimeMethod* method) ;
// GoogleMobileAds.Common.ILoadAdErrorClient GoogleMobileAds.Common.LoadAdErrorClientEventArgs::get_LoadAdErrorClient()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline (LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.LoadAdError::.ctor(GoogleMobileAds.Common.ILoadAdErrorClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.AppOpenAd::.ctor(GoogleMobileAds.Common.IAppOpenAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd__ctor_m8712CC46DE91737B91B231D8A52D26075994A20C (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>::Invoke(T1,T2)
inline void Action_2_Invoke_mAE86532DDFD293355C5CB21513641064870AC86D_inline (Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* __this, AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* ___0_arg1, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580*, AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC*, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*, const RuntimeMethod*))Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
// System.Void System.Action`1<GoogleMobileAds.Api.AdError>::Invoke(T)
inline void Action_1_Invoke_m1DAFB4BD1D193364D17C992CA9C35E091BEDBA56_inline (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* __this, AdError_tA3BF7518349885A73447524367693A096B3DD1AE* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*, AdError_tA3BF7518349885A73447524367693A096B3DD1AE*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
// System.Void System.Action`1<GoogleMobileAds.Api.AdValue>::Invoke(T)
inline void Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
// System.Void GoogleMobileAds.Api.ResponseInfo::.ctor(GoogleMobileAds.Common.IResponseInfoClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResponseInfo__ctor_m5CDD2ADA5EF001A9269D2A0BACF9390D0B044DFD (ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m0EA663E8A0E2B5707941BF2F08E4F4B32B72823E (U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_m10CBFCB9AF0E5B4775D5AAFB583930A64958EF88 (U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* __this, const RuntimeMethod* method) ;
// System.Void System.Action`1<GoogleMobileAds.Api.LoadAdError>::Invoke(T)
inline void Action_1_Invoke_m53B66B17CE3490A35AE80626192D1662CFAF9E25_inline (Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* __this, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m86A02532C966283C1C91EEBB986FAF423FB6FC5C (U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* __this, const RuntimeMethod* method) ;
// System.Boolean GoogleMobileAds.Api.InterstitialAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool InterstitialAd_CanShowAd_m76764B2A422FA2DE78D9E29DE630380BE6E65A0E (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m9A8FC0128A1F434EE1B3A709FEE69705B9424E5B (U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mDB67C29C7A47AEDD47CF4908138B0EEBA8BCA5F9 (U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_mE9E302BFC9D729F14AB85D057E5C81233F4C6AED (U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InterstitialAd::.ctor(GoogleMobileAds.Common.IInterstitialClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd__ctor_mF55A705F3B7BB445AE117E57F55A8B8D810854CA (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey2__ctor_m97059AC37568D3E45311BFEAC1BA0370E4345E2A (U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* __this, const RuntimeMethod* method) ;
// System.Void System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>::Invoke(T1,T2)
inline void Action_2_Invoke_mBEA6A0194FD6CCEAB0C00413E687A343A847BACB_inline (Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* __this, InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* ___0_arg1, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t6D103D50D92EED8806671DABC525008F7D560A68*, InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC*, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*, const RuntimeMethod*))Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
// System.Void GoogleMobileAds.Api.MobileAds::SetUnityMainThreadSynchronizationContext()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_SetUnityMainThreadSynchronizationContext_mC7C80D814660CF6742100D36D03F879120BBE27F (const RuntimeMethod* method) ;
// GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.Api.MobileAds::GetMobileAdsClient()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MobileAds_GetMobileAdsClient_m1A2ADAC0C61D673C13E8B06068EDFBACC58FCD14 (const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.MobileAds::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds__ctor_mE209BFED2C4B3EDEA77A1D92FA94E692454F9B10 (MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey0__ctor_m6DFCA2A06E9120467C5E1B941B84435F48BC9D74 (U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* __this, const RuntimeMethod* method) ;
// GoogleMobileAds.Api.MobileAds GoogleMobileAds.Api.MobileAds::get_Instance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A (const RuntimeMethod* method) ;
// System.Void System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>::.ctor(System.Object,System.IntPtr)
inline void Action_1__ctor_mC006F130CC140CE80CA285AAD5974413D196CCD3 (Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_1__ctor_m2E1DFA67718FC1A0B6E5DFEB78831FFE9C059EB4_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Void GoogleMobileAds.Common.MobileAdsEventExecutor::Initialize()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE (const RuntimeMethod* method) ;
// UnityEngine.RuntimePlatform UnityEngine.Application::get_platform()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Application_get_platform_m59EF7D6155D18891B24767F83F388160B1FF2138 (const RuntimeMethod* method) ;
// System.Object System.Activator::CreateInstance(System.Type)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Activator_CreateInstance_mFF030428C64FDDFACC74DFAC97388A1C628BFBCF (Type_t* ___0_type, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRaiseActionU3Ec__AnonStorey7__ctor_mBA567D38964549F7F8BD83C1B4AD5E28B50AC6E7 (U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* __this, const RuntimeMethod* method) ;
// System.Boolean GoogleMobileAds.Api.MobileAds::get_RaiseAdEventsOnUnityMainThread()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5_inline (const RuntimeMethod* method) ;
// System.Threading.Thread System.Threading.Thread::get_CurrentThread()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F* Thread_get_CurrentThread_m6D4719F4993DB9200490531FF02D4076FF9CA9BD (const RuntimeMethod* method) ;
// System.Int32 System.Threading.Thread::get_ManagedThreadId()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Thread_get_ManagedThreadId_m74ACB74A574EE535C2B00B7D64F203A62E796B05 (Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F* __this, const RuntimeMethod* method) ;
// System.Void System.Threading.SendOrPostCallback::.ctor(System.Object,System.IntPtr)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SendOrPostCallback__ctor_mE6F9D9606A00C3C18AEA057422ECF4106C80DA37 (SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
// System.Threading.SynchronizationContext System.Threading.SynchronizationContext::get_Current()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* SynchronizationContext_get_Current_m8DE6D3020745B7955249A2470A23EC0ECBB02A82 (const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey1__ctor_m8E0E7248D227077736A33E327508E616F92FCCF3 (U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.InitializationStatus::.ctor(GoogleMobileAds.Common.IInitializationStatusClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InitializationStatus__ctor_m1ECBC8D450C9F80E7D8A8F960722F3ED05AD2F15 (InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void System.Action`1<GoogleMobileAds.Api.InitializationStatus>::Invoke(T)
inline void Action_1_Invoke_m790B395C4CBEA3E5F364964EAC8520817D5C9A92_inline (Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* __this, InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE*, InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
// System.Void GoogleMobileAds.Api.RewardedAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_RegisterAdEvents_m87AD72161981214787EB23E154912B7576320C44 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m811853FCD1B2648F4113B428D255007083B1DDEA (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* __this, const RuntimeMethod* method) ;
// System.Boolean GoogleMobileAds.Api.RewardedAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RewardedAd_CanShowAd_m6DFCA02D9029DADED78F05E9590366781FCFFBC6 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) ;
// System.Void System.EventHandler`1<GoogleMobileAds.Api.Reward>::.ctor(System.Object,System.IntPtr)
inline void EventHandler_1__ctor_mB759CACFB208DA7C79637A2B4F041209D3CEC146 (EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC*, RuntimeObject*, intptr_t, const RuntimeMethod*))EventHandler_1__ctor_mBA2E748CFBA7A0EEC0A373A6F634D3B5774EA370_gshared)(__this, ___0_object, ___1_method, method);
}
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m0BFAEFFA6CDBF3C353153DAA828D50D74A2801C9 (U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3D4D7516A12306F7BDF65F140FEE86BAFC2DC69F (U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mCA18C893E9A99D7080F8FF71D49C20D9C5C7BEBE (U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m536F748986C564BA479A6ACEF9EF32F65CCD403D (U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedAd::.ctor(GoogleMobileAds.Common.IRewardedAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd__ctor_m25C00373D1190A96D49AE296BB1F51480DAF6340 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>::Invoke(T1,T2)
inline void Action_2_Invoke_mF6DAD8C7A08A67309D74DB8AF8A22DE7C2B4D8EC_inline (Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* __this, RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* ___0_arg1, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418*, RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09*, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*, const RuntimeMethod*))Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
// System.Void System.Action`1<GoogleMobileAds.Api.Reward>::Invoke(T)
inline void Action_1_Invoke_mD16ED15700A9B573C44A2A4CEF4CA51CACA79DCE_inline (Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* __this, Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* ___0_obj, const RuntimeMethod* method)
{
	((  void (*) (Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC*, Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511*, const RuntimeMethod*))Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline)(__this, ___0_obj, method);
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_RegisterAdEvents_mDDD067D0EA422486BB2BFC590E8D457C5F9EDCD4 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m72D0D4C8650D2D70F35636BEB5903C21DD1ECB85 (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* __this, const RuntimeMethod* method) ;
// System.Boolean GoogleMobileAds.Api.RewardedInterstitialAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RewardedInterstitialAd_CanShowAd_m374F4E205E398A48456BEC924CAA5DDD3A443CB0 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m2C583B4F9EFE0317753EB8FDA605DFCB04926F09 (U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3E834E57E2F90B6AFA9BF6D2FA8146EB240AA407 (U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mF94D025E5468729D99B7669F52F788661C5971EA (U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m2C44857B565DBA1D613F287A035A7E68D2E1EA88 (U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* __this, const RuntimeMethod* method) ;
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::.ctor(GoogleMobileAds.Common.IRewardedInterstitialAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd__ctor_mC4C75695E5B5F02F6D36BAEE3D7257CC5281EDE6 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) ;
// System.Void System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>::Invoke(T1,T2)
inline void Action_2_Invoke_m0CAC4772DC502A0F8D7DE0FED1691A7CE938A069_inline (Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* __this, RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* ___0_arg1, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* ___1_arg2, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E*, RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE*, LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*, const RuntimeMethod*))Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline)(__this, ___0_arg1, ___1_arg2, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AdError::.ctor(GoogleMobileAds.Common.IAdErrorClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07 (AdError_tA3BF7518349885A73447524367693A096B3DD1AE* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		RuntimeObject* L_0 = ___0_client;
		__this->____client_0 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_0), (void*)L_0);
		return;
	}
}
// System.String GoogleMobileAds.Api.AdError::GetMessage()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AdError_GetMessage_m1D90CEA0A4CE678016C5D06D73C3F111D6BA370C (AdError_tA3BF7518349885A73447524367693A096B3DD1AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAdErrorClient_t1070A9DCB8BBE68B67D9ECBC19949CDF415AD2BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_0;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = InterfaceFuncInvoker0< String_t* >::Invoke(0 /* System.String GoogleMobileAds.Common.IAdErrorClient::GetMessage() */, IAdErrorClient_t1070A9DCB8BBE68B67D9ECBC19949CDF415AD2BF_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
// System.String GoogleMobileAds.Api.AdError::ToString()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* AdError_ToString_mD2183315C9A1F6504900AD43F22BB385765FA9F9 (AdError_tA3BF7518349885A73447524367693A096B3DD1AE* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____client_0;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(3 /* System.String System.Object::ToString() */, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppOpenAd::.ctor(GoogleMobileAds.Common.IAppOpenAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd__ctor_m8712CC46DE91737B91B231D8A52D26075994A20C (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		__this->____canShowAd_7 = (bool)1;
		RuntimeObject* L_0 = ___0_client;
		__this->____client_6 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_6), (void*)L_0);
		AppOpenAd_RegisterAdEvents_m942A3CA51674726CDD0C3F6A9BDA83C0EF721B53(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_add_OnAdPaid_mC6A173F1C25378D51E0CA1EE26AC1D175E50918D (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_remove_OnAdPaid_mD7AE74221EA71AA54EEAA6F6B958FE68D69087AC (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_add_OnAdFullScreenContentOpened_m5BF498515C6EE855420C3C368C62385C2BF7F7BA (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_remove_OnAdFullScreenContentOpened_m522500F8D3143BB63D7E6227C22BE875B4E98B6A (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_add_OnAdFullScreenContentClosed_m9AFCAA1DF7CF8CCD46B3E62169ADA2B02D49C2CD (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_remove_OnAdFullScreenContentClosed_m4C89A4830D5EF6A6A51F0E163ED8048036B01546 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_add_OnAdFullScreenContentFailed_m4D52DC4F7D85A0DA305BAAAD27031C528006CE44 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_remove_OnAdFullScreenContentFailed_m578143F55D37FE0B507C12EBDF506B9DC2B97570 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.AppOpenAd,GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_Load_mF96F81FEF60491CD62225A17670A8D68D846F276 (String_t* ___0_adUnitId, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* ___1_request, Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* ___2_adLoadCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_0 = (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey0__ctor_mC3285F87E7CF1CDD7E8646879D08F71323457FFB(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_1 = V_0;
		Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* L_2 = ___2_adLoadCallback;
		NullCheck(L_1);
		L_1->___adLoadCallback_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adLoadCallback_0), (void*)L_2);
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_3 = V_0;
		NullCheck(L_3);
		Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* L_4 = L_3->___adLoadCallback_0;
		if (L_4)
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD, NULL);
		return;
	}

IL_0023:
	{
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_6;
		L_6 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_6);
		RuntimeObject* L_7;
		L_7 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(1 /* GoogleMobileAds.Common.IAppOpenAdClient GoogleMobileAds.IClientFactory::BuildAppOpenAdClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_6);
		NullCheck(L_5);
		L_5->___client_1 = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->___client_1), (void*)L_7);
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_8 = V_0;
		NullCheck(L_8);
		RuntimeObject* L_9 = L_8->___client_1;
		NullCheck(L_9);
		InterfaceActionInvoker0::Invoke(16 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::CreateAppOpenAd() */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_9);
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_10 = V_0;
		NullCheck(L_10);
		RuntimeObject* L_11 = L_10->___client_1;
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_12 = V_0;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_13 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_13, L_12, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3_RuntimeMethod_var), NULL);
		NullCheck(L_11);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_11, L_13);
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_14 = V_0;
		NullCheck(L_14);
		RuntimeObject* L_15 = L_14->___client_1;
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_16 = V_0;
		EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* L_17 = (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*)il2cpp_codegen_object_new(EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		NullCheck(L_17);
		EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9(L_17, L_16, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3_RuntimeMethod_var), NULL);
		NullCheck(L_15);
		InterfaceActionInvoker1< EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_15, L_17);
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_18 = V_0;
		NullCheck(L_18);
		RuntimeObject* L_19 = L_18->___client_1;
		String_t* L_20 = ___0_adUnitId;
		AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* L_21 = ___1_request;
		NullCheck(L_19);
		InterfaceActionInvoker2< String_t*, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* >::Invoke(17 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_19, L_20, L_21);
		return;
	}
}
// System.Boolean GoogleMobileAds.Api.AppOpenAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AppOpenAd_CanShowAd_m822270A031DF79CB0ED3AD52240BC4100F0DFAB1 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	int32_t G_B3_0 = 0;
	{
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		bool L_1 = __this->____canShowAd_7;
		G_B3_0 = ((int32_t)(L_1));
		goto IL_0014;
	}

IL_0013:
	{
		G_B3_0 = 0;
	}

IL_0014:
	{
		return (bool)G_B3_0;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::Show()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_Show_m2037E04CE3FAE7C677CEA702F39C20390CAB9024 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = AppOpenAd_CanShowAd_m822270A031DF79CB0ED3AD52240BC4100F0DFAB1(__this, NULL);
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(18 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::Show() */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::Destroy()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_Destroy_mD1F8CE10A274F9681F7D7FF7A591781F6A5E5351 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(19 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::DestroyAppOpenAd() */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_RegisterAdEvents_m942A3CA51674726CDD0C3F6A9BDA83C0EF721B53 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_6;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_1, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(14 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdClicked(System.Action) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_0, L_1);
		RuntimeObject* L_2 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_3 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_3, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(10 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_2, L_3);
		RuntimeObject* L_4 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_5 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_5, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(8 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_4, L_5);
		RuntimeObject* L_6 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_7 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_7, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(12 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_6, L_7);
		RuntimeObject* L_8 = __this->____client_6;
		EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* L_9 = (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E*)il2cpp_codegen_object_new(EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		NullCheck(L_9);
		EventHandler_1__ctor_mEB4BDC01DF31AFC470842A776CABEA9053594C64(L_9, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		InterfaceActionInvoker1< EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* >::Invoke(6 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_8, L_9);
		RuntimeObject* L_10 = __this->____client_6;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_11 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)il2cpp_codegen_object_new(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		NullCheck(L_11);
		Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9(L_11, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997_RuntimeMethod_var), NULL);
		NullCheck(L_10);
		InterfaceActionInvoker1< Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* >::Invoke(4 /* System.Void GoogleMobileAds.Common.IAppOpenAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>) */, IAppOpenAdClient_t808FB0D044FBF40A9CD2EB35FC6E9DFCF2A911D1_il2cpp_TypeInfo_var, L_10, L_11);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__0_m27A1F8B773ED5CDE8007F65DFDAFD05B60381651 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__1_m9C4BA7627EDA74D79F3EC01511390054892404AC (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__2_m699D6740C2F6D1033E611DC12965B18B303A4B2F (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__3_mB562EF2E861A7E54D09DE5A4A2A0F7157D4A0BAE (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__4_m58B88A7402B3A92907539DAD22DE4315A4821F14 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, RuntimeObject* ___0_sender, AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m960091194D004208900F150D954F8985848871B1(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* L_2 = V_0;
		AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline(L_3, NULL);
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_5 = (AdError_tA3BF7518349885A73447524367693A096B3DD1AE*)il2cpp_codegen_object_new(AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___adError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___adError_0), (void*)L_5);
		U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__5_mE9280DC930E3912764FEE6F905CDF95514891997 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_adValue, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m05E9BE4D156BBC16C191C438B22867C2C7142317(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* L_1 = V_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_2 = ___0_adValue;
		NullCheck(L_1);
		L_1->___adValue_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adValue_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__6()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__6_mDE5D7116D371A3AF16B62867E4171868A6CE0578 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdClicked_1;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdClicked_1;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__7_m2DE1DA8A99D930261053267DAACD6E5D5DFD0121 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentClosed_4;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__8()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__8_m3BB0C197A41B2420EB74BBB2EA03B87401E54923 (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentOpened_3;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd::<RegisterAdEvents>m__9()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppOpenAd_U3CRegisterAdEventsU3Em__9_mD5262901450D3C3E94AE21F828AC0C688B6D5E0C (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdImpressionRecorded_2;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdImpressionRecorded_2;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_mC3285F87E7CF1CDD7E8646879D08F71323457FFB (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m49C42950EE386C1E4C6044643D9521A264974CF3 (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mBB7E2D5335953D85AE80D33F59EDC5B3B2A455F3 (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* __this, RuntimeObject* ___0_sender, LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* L_0 = (U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey1__ctor_m536EDFA1E591F37667D32F2BB8F2CDA45711279A(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* L_2 = V_0;
		LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* L_3 = ___1_args;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline(L_3, NULL);
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_5 = (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)il2cpp_codegen_object_new(LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___loadAdError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___loadAdError_0), (void*)L_5);
		U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0::<>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mA3783D4C7BBE5618206F759BAA70878E3DDE1950 (U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* L_0 = __this->___adLoadCallback_0;
		RuntimeObject* L_1 = __this->___client_1;
		AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* L_2 = (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC*)il2cpp_codegen_object_new(AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC_il2cpp_TypeInfo_var);
		NullCheck(L_2);
		AppOpenAd__ctor_m8712CC46DE91737B91B231D8A52D26075994A20C(L_2, L_1, NULL);
		NullCheck(L_0);
		Action_2_Invoke_mAE86532DDFD293355C5CB21513641064870AC86D_inline(L_0, L_2, (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)NULL, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m536EDFA1E591F37667D32F2BB8F2CDA45711279A (U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1DFCB8AA6D258C3576F33092D33A9479496A74EB (U3CLoadU3Ec__AnonStorey1_t566C9C0E49096AC9ED1FABCCD90A74317EFD45EA* __this, const RuntimeMethod* method) 
{
	{
		U3CLoadU3Ec__AnonStorey0_tD96E499F21AE2084827FD476E9C0D076D9A2FDCC* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_2_t18685E5725B772AE298BCD6D1DC4E0B533F30580* L_1 = L_0->___adLoadCallback_0;
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_2 = __this->___loadAdError_0;
		NullCheck(L_1);
		Action_2_Invoke_mAE86532DDFD293355C5CB21513641064870AC86D_inline(L_1, (AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC*)NULL, L_2, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m960091194D004208900F150D954F8985848871B1 (U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m87FEA735EB953A658AE9766E47EBC3B9D318E538 (U3CRegisterAdEventsU3Ec__AnonStorey2_t2943AC652E7E9D5034DE1A76D69FD2E81830D866* __this, const RuntimeMethod* method) 
{
	{
		AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = L_0->___OnAdFullScreenContentFailed_5;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = L_2->___OnAdFullScreenContentFailed_5;
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_4 = __this->___adError_0;
		NullCheck(L_3);
		Action_1_Invoke_m1DAFB4BD1D193364D17C992CA9C35E091BEDBA56_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m05E9BE4D156BBC16C191C438B22867C2C7142317 (U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppOpenAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mEEB5DB9D7121D67FB5B1CF48EE12E0F96F002A20 (U3CRegisterAdEventsU3Ec__AnonStorey3_t5E7E3E23334DE6CB88734E90897DB0C8AB1F8954* __this, const RuntimeMethod* method) 
{
	{
		AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = L_0->___OnAdPaid_0;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		AppOpenAd_tF3D4C7489EF9689BE192F2379D10A39A26B82DAC* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = L_2->___OnAdPaid_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_4 = __this->___adValue_0;
		NullCheck(L_3);
		Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.AppStateEventNotifier::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppStateEventNotifier__cctor_m741EDC54C3A65311800A5B0AC88FAFF6A93A06B3 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0;
		L_0 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(0 /* GoogleMobileAds.Common.IAppStateEventClient GoogleMobileAds.IClientFactory::BuildAppStateEventClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_0);
		((AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_StaticFields*)il2cpp_codegen_static_fields_for(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var))->___client_0 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_StaticFields*)il2cpp_codegen_static_fields_for(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var))->___client_0), (void*)L_1);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppStateEventNotifier::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppStateEventNotifier_add_AppStateChanged_m11DAB106CCCE3EF78F377722C46ED63BCCF36704 (Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_StaticFields*)il2cpp_codegen_static_fields_for(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var))->___client_0;
		Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* L_1 = ___0_value;
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IAppStateEventClient::add_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>) */, IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73_il2cpp_TypeInfo_var, L_0, L_1);
		return;
	}
}
// System.Void GoogleMobileAds.Api.AppStateEventNotifier::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AppStateEventNotifier_remove_AppStateChanged_m9C6E767D608F2F692DF3BEA4DB4EA9FE615E5364 (Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_StaticFields*)il2cpp_codegen_static_fields_for(AppStateEventNotifier_t0A047576C80CBFF891C1E1A0106BA70A5E302FAE_il2cpp_TypeInfo_var))->___client_0;
		Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* L_1 = ___0_value;
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_1_tFF28F60745C112FCD21E71B64003D8D58F407B79* >::Invoke(1 /* System.Void GoogleMobileAds.Common.IAppStateEventClient::remove_AppStateChanged(System.Action`1<GoogleMobileAds.Common.AppState>) */, IAppStateEventClient_t1ADD98C9FF5BF82F2B549C910DC8727FF655EF73_il2cpp_TypeInfo_var, L_0, L_1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.BannerView::.ctor(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView__ctor_m3C156986754CF612D73D61061B92BE3468FF6FDC (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, String_t* ___0_adUnitId, AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* ___1_adSize, int32_t ___2_position, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0;
		L_0 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(2 /* GoogleMobileAds.Common.IBannerClient GoogleMobileAds.IClientFactory::BuildBannerClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_0);
		__this->____client_7 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_7), (void*)L_1);
		RuntimeObject* L_2 = __this->____client_7;
		String_t* L_3 = ___0_adUnitId;
		AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158* L_4 = ___1_adSize;
		int32_t L_5 = ___2_position;
		NullCheck(L_2);
		InterfaceActionInvoker3< String_t*, AdSize_tF9FBB34C7252D73803A62C13A3AB656A06A0F158*, int32_t >::Invoke(14 /* System.Void GoogleMobileAds.Common.IBannerClient::CreateBannerView(System.String,GoogleMobileAds.Api.AdSize,GoogleMobileAds.Api.AdPosition) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_2, L_3, L_4, L_5);
		VirtualActionInvoker0::Invoke(4 /* System.Void GoogleMobileAds.Api.BannerView::ConfigureBannerEvents() */, __this);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoaded(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_add_OnBannerAdLoaded_mF3A6AA44B521D6AFF697B4FDF73C02139F1DEAE6 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnBannerAdLoaded_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnBannerAdLoaded_0);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoaded(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_remove_OnBannerAdLoaded_mAF3A48277FF13423864DFB8A25DB7688F51BEEA9 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnBannerAdLoaded_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnBannerAdLoaded_0);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::add_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_add_OnBannerAdLoadFailed_m90B2772E9E91E19DA78644420A09DB4EC190597C (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* V_0 = NULL;
	Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* V_1 = NULL;
	{
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_0 = __this->___OnBannerAdLoadFailed_1;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C** L_2 = (Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C**)(&__this->___OnBannerAdLoadFailed_1);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_3 = V_1;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_6 = V_0;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*>(L_2, ((Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)Castclass((RuntimeObject*)L_5, Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_8 = V_0;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)L_8) == ((RuntimeObject*)(Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::remove_OnBannerAdLoadFailed(System.Action`1<GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_remove_OnBannerAdLoadFailed_m3515C651D8F4110C5C4E0CD28C88478619089635 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* V_0 = NULL;
	Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* V_1 = NULL;
	{
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_0 = __this->___OnBannerAdLoadFailed_1;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_1 = V_0;
		V_1 = L_1;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C** L_2 = (Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C**)(&__this->___OnBannerAdLoadFailed_1);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_3 = V_1;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_6 = V_0;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*>(L_2, ((Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)Castclass((RuntimeObject*)L_5, Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_8 = V_0;
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)L_8) == ((RuntimeObject*)(Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::Destroy()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_Destroy_mCD660269DF04D3EA92CC22EC1045B00C1E6CDC01 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(18 /* System.Void GoogleMobileAds.Common.IBannerClient::DestroyBannerView() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
		__this->____client_7 = (RuntimeObject*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_7), (void*)(RuntimeObject*)NULL);
	}

IL_001d:
	{
		return;
	}
}
// GoogleMobileAds.Api.ResponseInfo GoogleMobileAds.Api.BannerView::GetResponseInfo()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* BannerView_GetResponseInfo_m9FB398199E74F3AB52D8BF16121E426E9FD06D4C (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* G_B3_0 = NULL;
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_0020;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		RuntimeObject* L_2;
		L_2 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(21 /* GoogleMobileAds.Common.IResponseInfoClient GoogleMobileAds.Common.IBannerClient::GetResponseInfoClient() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
		ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* L_3 = (ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0*)il2cpp_codegen_object_new(ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		ResponseInfo__ctor_m5CDD2ADA5EF001A9269D2A0BACF9390D0B044DFD(L_3, L_2, NULL);
		G_B3_0 = L_3;
		goto IL_0021;
	}

IL_0020:
	{
		G_B3_0 = ((ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0*)(NULL));
	}

IL_0021:
	{
		return G_B3_0;
	}
}
// System.Single GoogleMobileAds.Api.BannerView::GetHeightInPixels()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float BannerView_GetHeightInPixels_m7E9AEE3A333CC8370B35DBF8E8C6ED89EFDEB4B8 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float G_B3_0 = 0.0f;
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		float L_2;
		L_2 = InterfaceFuncInvoker0< float >::Invoke(19 /* System.Single GoogleMobileAds.Common.IBannerClient::GetHeightInPixels() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
		G_B3_0 = L_2;
		goto IL_0020;
	}

IL_001b:
	{
		G_B3_0 = (0.0f);
	}

IL_0020:
	{
		return G_B3_0;
	}
}
// System.Single GoogleMobileAds.Api.BannerView::GetWidthInPixels()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float BannerView_GetWidthInPixels_m30250357790E167D473D206B4586A6EA6792A8ED (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float G_B3_0 = 0.0f;
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		float L_2;
		L_2 = InterfaceFuncInvoker0< float >::Invoke(20 /* System.Single GoogleMobileAds.Common.IBannerClient::GetWidthInPixels() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
		G_B3_0 = L_2;
		goto IL_0020;
	}

IL_001b:
	{
		G_B3_0 = (0.0f);
	}

IL_0020:
	{
		return G_B3_0;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::LoadAd(GoogleMobileAds.Api.AdRequest)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_LoadAd_m56BF83CC97DE20C40457B9452D0ABEFD97933FFF (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* ___0_request, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_0017;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* L_2 = ___0_request;
		NullCheck(L_1);
		InterfaceActionInvoker1< AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* >::Invoke(15 /* System.Void GoogleMobileAds.Common.IBannerClient::LoadAd(GoogleMobileAds.Api.AdRequest) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1, L_2);
	}

IL_0017:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::Show()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_Show_m90ACC1B7ED13065667AB7948722F0B2CBD3A84AE (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(16 /* System.Void GoogleMobileAds.Common.IBannerClient::ShowBannerView() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::Hide()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_Hide_m54630CB9110A9D38DFC00BC047B775C778EA22BD (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_7;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_7;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(17 /* System.Void GoogleMobileAds.Common.IBannerClient::HideBannerView() */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_1);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::ConfigureBannerEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_ConfigureBannerEvents_m04F0638EF968385C04608F12F9BBFF1811F1437A (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_7;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_1 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_1, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_0, L_1);
		RuntimeObject* L_2 = __this->____client_7;
		EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* L_3 = (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*)il2cpp_codegen_object_new(EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9(L_3, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		InterfaceActionInvoker1< EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_2, L_3);
		RuntimeObject* L_4 = __this->____client_7;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_5 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_5, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(4 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdOpening(System.EventHandler`1<System.EventArgs>) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_4, L_5);
		RuntimeObject* L_6 = __this->____client_7;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_7 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_7, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(6 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClosed(System.EventHandler`1<System.EventArgs>) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_6, L_7);
		RuntimeObject* L_8 = __this->____client_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)il2cpp_codegen_object_new(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		NullCheck(L_9);
		Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9(L_9, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		InterfaceActionInvoker1< Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* >::Invoke(8 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_8, L_9);
		RuntimeObject* L_10 = __this->____client_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_11 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_11);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_11, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C_RuntimeMethod_var), NULL);
		NullCheck(L_10);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(10 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdClicked(System.Action) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_10, L_11);
		RuntimeObject* L_12 = __this->____client_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_13 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_13, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643_RuntimeMethod_var), NULL);
		NullCheck(L_12);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(12 /* System.Void GoogleMobileAds.Common.IBannerClient::add_OnAdImpressionRecorded(System.Action) */, IBannerClient_t09294BA982EA240BD81470F2CFB8DA17E6D7A42D_il2cpp_TypeInfo_var, L_12, L_13);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__0(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__0_m950132C20D9FF852333840439DE8B7E912AED86E (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__1_m33285EB72E7B98E9DDE1A768A6E9AED39973E367 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, RuntimeObject* ___0_sender, LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* V_0 = NULL;
	{
		U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* L_0 = (U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159*)il2cpp_codegen_object_new(U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m0EA663E8A0E2B5707941BF2F08E4F4B32B72823E(L_0, NULL);
		V_0 = L_0;
		U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U24this_1), (void*)__this);
		U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* L_2 = V_0;
		LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* L_3 = ___1_args;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline(L_3, NULL);
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_5 = (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)il2cpp_codegen_object_new(LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___loadAdError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___loadAdError_0), (void*)L_5);
		U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__2(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__2_mE7CB7EF05F18F046FEFE2051610EADC5F7DB26B1 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__3(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__3_mA1831E8828F53A1179D0A9FEB046D01B6C6FFC9D (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__4(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__4_m7145FAC85AB6EEA6289BA99FE9776DFF4CE4B876 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_adValue, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* V_0 = NULL;
	{
		U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* L_0 = (U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0*)il2cpp_codegen_object_new(U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_m10CBFCB9AF0E5B4775D5AAFB583930A64958EF88(L_0, NULL);
		V_0 = L_0;
		U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* L_1 = V_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_2 = ___0_adValue;
		NullCheck(L_1);
		L_1->___adValue_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adValue_0), (void*)L_2);
		U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__5()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__5_m16FA9EC5112461943D6912C09B316B1D0878581C (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__6()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__6_m6850799F2427F7E1098F13861874B2C678534643 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__7_m9AF0E557AEE8A07DD4222E3FCD89B5105BA5D077 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnBannerAdLoaded_0;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnBannerAdLoaded_0;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__8()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__8_m3A928D0457E7D80A84DC9309BD0E3B94EB8ACFC2 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_5;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentOpened_5;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__9()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__9_mE15693E2BB35B4112B0B3085064CD0AF30DF6071 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_6;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentClosed_6;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__A()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__A_m6B9B50D77E6054F2863F2A1762CC3C167947F202 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdClicked_3;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdClicked_3;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView::<ConfigureBannerEvents>m__B()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BannerView_U3CConfigureBannerEventsU3Em__B_m69174F10E32D424CB92DD84998F2FB04CFF5D9F8 (BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdImpressionRecorded_4;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdImpressionRecorded_4;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey0__ctor_m0EA663E8A0E2B5707941BF2F08E4F4B32B72823E (U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey0::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey0_U3CU3Em__0_mB8F05C7553294A657561161F2EBEF25060CDB69B (U3CConfigureBannerEventsU3Ec__AnonStorey0_tC78496E8E0B5D9B37DFD7DB9B5440BAC94753159* __this, const RuntimeMethod* method) 
{
	{
		BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_1 = L_0->___OnBannerAdLoadFailed_1;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t841B459F75B339DA7C5B42D7EA2E177BAE679C4C* L_3 = L_2->___OnBannerAdLoadFailed_1;
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_4 = __this->___loadAdError_0;
		NullCheck(L_3);
		Action_1_Invoke_m53B66B17CE3490A35AE80626192D1662CFAF9E25_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey1__ctor_m10CBFCB9AF0E5B4775D5AAFB583930A64958EF88 (U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.BannerView/<ConfigureBannerEvents>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CConfigureBannerEventsU3Ec__AnonStorey1_U3CU3Em__0_m9045774A489D908E618F158776547721382934C4 (U3CConfigureBannerEventsU3Ec__AnonStorey1_tF4FE247D0F4BE902D37FBBA2041DDB61C489D8D0* __this, const RuntimeMethod* method) 
{
	{
		BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = L_0->___OnAdPaid_2;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		BannerView_t203A90A6D41B56EC7208F121D6EE7E7283A590BB* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = L_2->___OnAdPaid_2;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_4 = __this->___adValue_0;
		NullCheck(L_3);
		Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InitializationStatus::.ctor(GoogleMobileAds.Common.IInitializationStatusClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InitializationStatus__ctor_m1ECBC8D450C9F80E7D8A8F960722F3ED05AD2F15 (InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		RuntimeObject* L_0 = ___0_client;
		__this->___client_0 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___client_0), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd::.ctor(GoogleMobileAds.Common.IInterstitialClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd__ctor_mF55A705F3B7BB445AE117E57F55A8B8D810854CA (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		RuntimeObject* L_0 = ___0_client;
		__this->____client_6 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_6), (void*)L_0);
		__this->____canShowAd_7 = (bool)1;
		VirtualActionInvoker0::Invoke(4 /* System.Void GoogleMobileAds.Api.InterstitialAd::RegisterAdEvents() */, __this);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_add_OnAdPaid_m3B50D1F1600BDC3552B5A4908A49F5FEA7FFD1A3 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_remove_OnAdPaid_m718C856E51DBFBB28E130954FAE8FD20EC5301ED (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_add_OnAdFullScreenContentOpened_mB5E6127049339D2D6BB4ACB8DBBE03A68C4A09AF (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_remove_OnAdFullScreenContentOpened_mC6B904E24552B94D85EF1949A475C986F0636179 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_add_OnAdFullScreenContentClosed_m752CF051CA5A0DA7F958D5C3A1813F30D09030E7 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_remove_OnAdFullScreenContentClosed_m51AAA11D9E43921F8E442BC3CC4888117AA7A0E5 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_add_OnAdFullScreenContentFailed_m0A88B3B74B846FCA5CF7BB461E8ABF98FC269C85 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_remove_OnAdFullScreenContentFailed_mED3D4983E4BF4AA5FB76264B5BA9693C8BF3D170 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.InterstitialAd,GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_Load_m7582DD601991C297C7E7E61BBE53B639466A73A1 (String_t* ___0_adUnitId, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* ___1_request, Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* ___2_adLoadCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_0 = (U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey0__ctor_m86A02532C966283C1C91EEBB986FAF423FB6FC5C(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_1 = V_0;
		Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* L_2 = ___2_adLoadCallback;
		NullCheck(L_1);
		L_1->___adLoadCallback_1 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adLoadCallback_1), (void*)L_2);
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_3 = V_0;
		NullCheck(L_3);
		Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* L_4 = L_3->___adLoadCallback_1;
		if (L_4)
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD, NULL);
		return;
	}

IL_0023:
	{
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_6;
		L_6 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_6);
		RuntimeObject* L_7;
		L_7 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(3 /* GoogleMobileAds.Common.IInterstitialClient GoogleMobileAds.IClientFactory::BuildInterstitialClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_6);
		NullCheck(L_5);
		L_5->___client_0 = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->___client_0), (void*)L_7);
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_8 = V_0;
		NullCheck(L_8);
		RuntimeObject* L_9 = L_8->___client_0;
		NullCheck(L_9);
		InterfaceActionInvoker0::Invoke(16 /* System.Void GoogleMobileAds.Common.IInterstitialClient::CreateInterstitialAd() */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_9);
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_10 = V_0;
		NullCheck(L_10);
		RuntimeObject* L_11 = L_10->___client_0;
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_12 = V_0;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_13 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_13, L_12, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20_RuntimeMethod_var), NULL);
		NullCheck(L_11);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_11, L_13);
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_14 = V_0;
		NullCheck(L_14);
		RuntimeObject* L_15 = L_14->___client_0;
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_16 = V_0;
		EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* L_17 = (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*)il2cpp_codegen_object_new(EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		NullCheck(L_17);
		EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9(L_17, L_16, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B_RuntimeMethod_var), NULL);
		NullCheck(L_15);
		InterfaceActionInvoker1< EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_15, L_17);
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_18 = V_0;
		NullCheck(L_18);
		RuntimeObject* L_19 = L_18->___client_0;
		String_t* L_20 = ___0_adUnitId;
		AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* L_21 = ___1_request;
		NullCheck(L_19);
		InterfaceActionInvoker2< String_t*, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* >::Invoke(17 /* System.Void GoogleMobileAds.Common.IInterstitialClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_19, L_20, L_21);
		return;
	}
}
// System.Boolean GoogleMobileAds.Api.InterstitialAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool InterstitialAd_CanShowAd_m76764B2A422FA2DE78D9E29DE630380BE6E65A0E (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	int32_t G_B3_0 = 0;
	{
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		bool L_1 = __this->____canShowAd_7;
		G_B3_0 = ((int32_t)(L_1));
		goto IL_0014;
	}

IL_0013:
	{
		G_B3_0 = 0;
	}

IL_0014:
	{
		return (bool)G_B3_0;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::Show()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_Show_m70188D3BE2543E7A0B58579A5991DA5A9E11CBAB (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = InterstitialAd_CanShowAd_m76764B2A422FA2DE78D9E29DE630380BE6E65A0E(__this, NULL);
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(18 /* System.Void GoogleMobileAds.Common.IInterstitialClient::Show() */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::Destroy()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_Destroy_m5C5C9367FF6A3500C3B61FD2BDBE5B6AF2FF0778 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(19 /* System.Void GoogleMobileAds.Common.IInterstitialClient::DestroyInterstitial() */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_RegisterAdEvents_m9FB37519C9D7FB812BAA0363DEE49DA56F858309 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_6;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_1, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(14 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdClicked(System.Action) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_0, L_1);
		RuntimeObject* L_2 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_3 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_3, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(10 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_2, L_3);
		RuntimeObject* L_4 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_5 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_5, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(8 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_4, L_5);
		RuntimeObject* L_6 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_7 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_7, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(12 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_6, L_7);
		RuntimeObject* L_8 = __this->____client_6;
		EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* L_9 = (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E*)il2cpp_codegen_object_new(EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		NullCheck(L_9);
		EventHandler_1__ctor_mEB4BDC01DF31AFC470842A776CABEA9053594C64(L_9, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		InterfaceActionInvoker1< EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* >::Invoke(6 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_8, L_9);
		RuntimeObject* L_10 = __this->____client_6;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_11 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)il2cpp_codegen_object_new(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		NullCheck(L_11);
		Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9(L_11, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89_RuntimeMethod_var), NULL);
		NullCheck(L_10);
		InterfaceActionInvoker1< Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* >::Invoke(4 /* System.Void GoogleMobileAds.Common.IInterstitialClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>) */, IInterstitialClient_t18D2E5ED7E33938C3CC2556409B01959F8E4C784_il2cpp_TypeInfo_var, L_10, L_11);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__0_m15F794DFA1B2D15AF3FD46DD0535B35E8E39BD70 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__1_m77C5D5D530946D3B60F32AE12E82C6730C8CE18F (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__2_m694739D06B2C43CAF858120C9F931BC94B9E80BF (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__3_m47237067EBB70CD501CC51CB495AEF7AA00C8194 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__4_mA938230C35A0425F720B36F5E01E1F1395BEE9C8 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, RuntimeObject* ___0_sender, AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m9A8FC0128A1F434EE1B3A709FEE69705B9424E5B(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* L_2 = V_0;
		AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline(L_3, NULL);
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_5 = (AdError_tA3BF7518349885A73447524367693A096B3DD1AE*)il2cpp_codegen_object_new(AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___adError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___adError_0), (void*)L_5);
		U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__5_m457A38FDD193CDBAD62505C2F50A8169E425AD89 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_adValue, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mDB67C29C7A47AEDD47CF4908138B0EEBA8BCA5F9(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* L_1 = V_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_2 = ___0_adValue;
		NullCheck(L_1);
		L_1->___adValue_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adValue_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__6()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__6_m6AE447D420E294BFA27C74F33F6AB7AB24C12D25 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdClicked_1;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdClicked_1;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__7_m45C19CF4E9FD6FBD9C1232A71CD3FFA354F4D91E (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentClosed_4;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__8()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__8_mA46671F488D6BB9275D7AFB99691B279995E82F3 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentOpened_3;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd::<RegisterAdEvents>m__9()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InterstitialAd_U3CRegisterAdEventsU3Em__9_m990102B76D6D8BA076566A3057B1EEE9ABAF1759 (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdImpressionRecorded_2;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdImpressionRecorded_2;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m86A02532C966283C1C91EEBB986FAF423FB6FC5C (U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m631331ABD2466F9B2AE59D8DFF7723E436983B20 (U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* L_0 = (U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey1__ctor_mE9E302BFC9D729F14AB85D057E5C81233F4C6AED(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* L_2 = V_0;
		RuntimeObject* L_3 = __this->___client_0;
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_4 = (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC*)il2cpp_codegen_object_new(InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC_il2cpp_TypeInfo_var);
		NullCheck(L_4);
		InterstitialAd__ctor_mF55A705F3B7BB445AE117E57F55A8B8D810854CA(L_4, L_3, NULL);
		NullCheck(L_2);
		L_2->___interstitialAd_0 = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___interstitialAd_0), (void*)L_4);
		U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* L_5 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_6);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_6, L_5, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_6, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mCDAD16B0470ADCBA74835F69B270E5174950D92B (U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* __this, RuntimeObject* ___0_sender, LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* L_0 = (U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey2__ctor_m97059AC37568D3E45311BFEAC1BA0370E4345E2A(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* L_2 = V_0;
		LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline(L_3, NULL);
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_5 = (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)il2cpp_codegen_object_new(LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___loadAdError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___loadAdError_0), (void*)L_5);
		U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_mE9E302BFC9D729F14AB85D057E5C81233F4C6AED (U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m64E945F2EC125802BFC95173075BF6ABE5438E52 (U3CLoadU3Ec__AnonStorey1_t078F80D6238FA3404C369261E17E8C11E357793A* __this, const RuntimeMethod* method) 
{
	{
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* L_1 = L_0->___adLoadCallback_1;
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_2 = __this->___interstitialAd_0;
		NullCheck(L_1);
		Action_2_Invoke_mBEA6A0194FD6CCEAB0C00413E687A343A847BACB_inline(L_1, L_2, (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)NULL, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey2__ctor_m97059AC37568D3E45311BFEAC1BA0370E4345E2A (U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey2::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey2_U3CU3Em__0_m0132A6A39D8BDC7093D0C265AA5BCFCE1B8417DC (U3CLoadU3Ec__AnonStorey2_tBC584DF9FE468ED28241B68AC750C2D40AD73B73* __this, const RuntimeMethod* method) 
{
	{
		U3CLoadU3Ec__AnonStorey0_t0A670E82EC373473D0DFD058F62D65E411B396F6* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_2_t6D103D50D92EED8806671DABC525008F7D560A68* L_1 = L_0->___adLoadCallback_1;
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_2 = __this->___loadAdError_0;
		NullCheck(L_1);
		Action_2_Invoke_mBEA6A0194FD6CCEAB0C00413E687A343A847BACB_inline(L_1, (InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC*)NULL, L_2, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m9A8FC0128A1F434EE1B3A709FEE69705B9424E5B (U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_mA89B79A7FE9DB7FBB22C1E1B20F33DBA46B20D7F (U3CRegisterAdEventsU3Ec__AnonStorey3_tF7328B04881DDB8E28037B8716B7B3E0EC0A5F15* __this, const RuntimeMethod* method) 
{
	{
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = L_0->___OnAdFullScreenContentFailed_5;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = L_2->___OnAdFullScreenContentFailed_5;
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_4 = __this->___adError_0;
		NullCheck(L_3);
		Action_1_Invoke_m1DAFB4BD1D193364D17C992CA9C35E091BEDBA56_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mDB67C29C7A47AEDD47CF4908138B0EEBA8BCA5F9 (U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.InterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_mCE5D71C37A91D8E6C882E40EECBA76A8EF23E74B (U3CRegisterAdEventsU3Ec__AnonStorey4_t68C06B745C8ACE1C5A2FEA8CF032881194C8E9FD* __this, const RuntimeMethod* method) 
{
	{
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = L_0->___OnAdPaid_0;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		InterstitialAd_t41640B3957C2B66FA5F6F38A4805393B6B013FEC* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = L_2->___OnAdPaid_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_4 = __this->___adValue_0;
		NullCheck(L_3);
		Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.LoadAdError::.ctor(GoogleMobileAds.Common.ILoadAdErrorClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ___0_client;
		AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07(__this, L_0, NULL);
		RuntimeObject* L_1 = ___0_client;
		__this->____client_1 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_1), (void*)L_1);
		return;
	}
}
// System.String GoogleMobileAds.Api.LoadAdError::ToString()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* LoadAdError_ToString_mC2DBA6C870EEBF993492FF04A29F4AE567D45C53 (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____client_1;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(3 /* System.String System.Object::ToString() */, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.MobileAds::.cctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds__cctor_mB05D140F842D0D647DE6DBA9F70D77B8565984F9 (const RuntimeMethod* method) 
{
	{
		MobileAds_SetUnityMainThreadSynchronizationContext_mC7C80D814660CF6742100D36D03F879120BBE27F(NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds__ctor_mE209BFED2C4B3EDEA77A1D92FA94E692454F9B10 (MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0;
		L_0 = MobileAds_GetMobileAdsClient_m1A2ADAC0C61D673C13E8B06068EDFBACC58FCD14(NULL);
		__this->___client_0 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___client_0), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// GoogleMobileAds.Api.MobileAds GoogleMobileAds.Api.MobileAds::get_Instance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_0 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___instance_4;
		if (L_0)
		{
			goto IL_0014;
		}
	}
	{
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_1 = (MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5*)il2cpp_codegen_object_new(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		MobileAds__ctor_mE209BFED2C4B3EDEA77A1D92FA94E692454F9B10(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___instance_4 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___instance_4), (void*)L_1);
	}

IL_0014:
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_2 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___instance_4;
		return L_2;
	}
}
// System.Boolean GoogleMobileAds.Api.MobileAds::get_RaiseAdEventsOnUnityMainThread()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		bool L_0 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField_5;
		return L_0;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::set_RaiseAdEventsOnUnityMainThread(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_set_RaiseAdEventsOnUnityMainThread_mCC9684511E440AD0F0D20CA15E36AE96BB552A9C (bool ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField_5 = L_0;
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::Initialize(System.Action`1<GoogleMobileAds.Api.InitializationStatus>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_Initialize_m9151E3C38E0FAD4439DA5020A217BAB90ABB76AD (Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* ___0_initCompleteAction, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* V_0 = NULL;
	{
		U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* L_0 = (U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E*)il2cpp_codegen_object_new(U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CInitializeU3Ec__AnonStorey0__ctor_m6DFCA2A06E9120467C5E1B941B84435F48BC9D74(L_0, NULL);
		V_0 = L_0;
		U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* L_1 = V_0;
		Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* L_2 = ___0_initCompleteAction;
		NullCheck(L_1);
		L_1->___initCompleteAction_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___initCompleteAction_0), (void*)L_2);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_3;
		L_3 = MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A(NULL);
		NullCheck(L_3);
		RuntimeObject* L_4 = L_3->___client_0;
		U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* L_5 = V_0;
		Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499* L_6 = (Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499*)il2cpp_codegen_object_new(Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499_il2cpp_TypeInfo_var);
		NullCheck(L_6);
		Action_1__ctor_mC006F130CC140CE80CA285AAD5974413D196CCD3(L_6, L_5, (intptr_t)((void*)U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< Action_1_t2C0F993ABBCCB1EEED0F494A48A590F2616E0499* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IMobileAdsClient::Initialize(System.Action`1<GoogleMobileAds.Common.IInitializationStatusClient>) */, IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var, L_4, L_6);
		il2cpp_codegen_runtime_class_init_inline(MobileAdsEventExecutor_t1185FA76E60746A4BF6150BBFC4BD17B123F380A_il2cpp_TypeInfo_var);
		MobileAdsEventExecutor_Initialize_m76A4DE8CDB2F00244DF305ACE913CDA65750ADEE(NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_SetRequestConfiguration_m1F0110671038CF67255ADAA3CFDB4CFC6E1E7AF5 (RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB* ___0_requestConfiguration, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_0;
		L_0 = MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A(NULL);
		NullCheck(L_0);
		RuntimeObject* L_1 = L_0->___client_0;
		RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB* L_2 = ___0_requestConfiguration;
		NullCheck(L_1);
		InterfaceActionInvoker1< RequestConfiguration_tE715340E972C7F6E659FD3ACE136CDDEAD74CCCB* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IMobileAdsClient::SetRequestConfiguration(GoogleMobileAds.Api.RequestConfiguration) */, IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var, L_1, L_2);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::SetiOSAppPauseOnBackground(System.Boolean)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_SetiOSAppPauseOnBackground_m2626F076EEDF748681C27AC832E79AC9E93756E7 (bool ___0_pause, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5* L_0;
		L_0 = MobileAds_get_Instance_m90A099F9597B22FEEC2CB4739F31219727C3614A(NULL);
		NullCheck(L_0);
		RuntimeObject* L_1 = L_0->___client_0;
		bool L_2 = ___0_pause;
		NullCheck(L_1);
		InterfaceActionInvoker1< bool >::Invoke(1 /* System.Void GoogleMobileAds.Common.IMobileAdsClient::SetiOSAppPauseOnBackground(System.Boolean) */, IMobileAdsClient_tA147A8D3D422203A033C8F34FE931A06B1FEB97F_il2cpp_TypeInfo_var, L_1, L_2);
		return;
	}
}
// GoogleMobileAds.IClientFactory GoogleMobileAds.Api.MobileAds::GetClientFactory()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_GetType_m71A077E0B5DA3BD1DC0AB9AE387056CFCF56F93F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E40C18D8AC4A6D9A62D3A099D09AF791342A6DB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB0496525135724A6D7138AB94BD6661514AD7DAE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBDD22D9991854F9B5ADA35715330812319A1362B);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	Type_t* V_1 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___clientFactory_1;
		if (L_0)
		{
			goto IL_0056;
		}
	}
	{
		V_0 = (String_t*)NULL;
		int32_t L_1;
		L_1 = Application_get_platform_m59EF7D6155D18891B24767F83F388160B1FF2138(NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)8))))
		{
			goto IL_0022;
		}
	}
	{
		V_0 = _stringLiteral1E40C18D8AC4A6D9A62D3A099D09AF791342A6DB;
		goto IL_003f;
	}

IL_0022:
	{
		int32_t L_2;
		L_2 = Application_get_platform_m59EF7D6155D18891B24767F83F388160B1FF2138(NULL);
		if ((!(((uint32_t)L_2) == ((uint32_t)((int32_t)11)))))
		{
			goto IL_0039;
		}
	}
	{
		V_0 = _stringLiteralBDD22D9991854F9B5ADA35715330812319A1362B;
		goto IL_003f;
	}

IL_0039:
	{
		V_0 = _stringLiteralB0496525135724A6D7138AB94BD6661514AD7DAE;
	}

IL_003f:
	{
		String_t* L_3 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_4;
		L_4 = il2cpp_codegen_get_type(L_3, Type_GetType_m71A077E0B5DA3BD1DC0AB9AE387056CFCF56F93F_RuntimeMethod_var, MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E_RuntimeMethod_var);
		V_1 = L_4;
		Type_t* L_5 = V_1;
		RuntimeObject* L_6;
		L_6 = Activator_CreateInstance_mFF030428C64FDDFACC74DFAC97388A1C628BFBCF(L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___clientFactory_1 = ((RuntimeObject*)Castclass((RuntimeObject*)L_6, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___clientFactory_1), (void*)((RuntimeObject*)Castclass((RuntimeObject*)L_6, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var)));
	}

IL_0056:
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_7 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___clientFactory_1;
		return L_7;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::RaiseAction(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* V_0 = NULL;
	{
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_0 = (U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653*)il2cpp_codegen_object_new(U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRaiseActionU3Ec__AnonStorey7__ctor_mBA567D38964549F7F8BD83C1B4AD5E28B50AC6E7(L_0, NULL);
		V_0 = L_0;
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_1 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = ___0_action;
		NullCheck(L_1);
		L_1->___action_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___action_0), (void*)L_2);
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_3 = V_0;
		NullCheck(L_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = L_3->___action_0;
		if (L_4)
		{
			goto IL_0019;
		}
	}
	{
		return;
	}

IL_0019:
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5_inline(NULL);
		if (!L_5)
		{
			goto IL_0041;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* L_6 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____synchronizationContext_2;
		if (!L_6)
		{
			goto IL_0041;
		}
	}
	{
		Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F* L_7;
		L_7 = Thread_get_CurrentThread_m6D4719F4993DB9200490531FF02D4076FF9CA9BD(NULL);
		NullCheck(L_7);
		int32_t L_8;
		L_8 = Thread_get_ManagedThreadId_m74ACB74A574EE535C2B00B7D64F203A62E796B05(L_7, NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		int32_t L_9 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____unityMainThreadId_3;
		if ((!(((uint32_t)L_8) == ((uint32_t)L_9))))
		{
			goto IL_004d;
		}
	}

IL_0041:
	{
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_10 = V_0;
		NullCheck(L_10);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_11 = L_10->___action_0;
		NullCheck(L_11);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_11, NULL);
		return;
	}

IL_004d:
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* L_12 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____synchronizationContext_2;
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_13 = V_0;
		SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E* L_14 = (SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E*)il2cpp_codegen_object_new(SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E_il2cpp_TypeInfo_var);
		NullCheck(L_14);
		SendOrPostCallback__ctor_mE6F9D9606A00C3C18AEA057422ECF4106C80DA37(L_14, L_13, (intptr_t)((void*)U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE_RuntimeMethod_var), NULL);
		U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* L_15 = V_0;
		NullCheck(L_15);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_16 = L_15->___action_0;
		NullCheck(L_12);
		VirtualActionInvoker2< SendOrPostCallback_t5C292A12062F24027A98492F52ECFE9802AA6F0E*, RuntimeObject* >::Invoke(5 /* System.Void System.Threading.SynchronizationContext::Post(System.Threading.SendOrPostCallback,System.Object) */, L_12, L_14, L_16);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds::SetUnityMainThreadSynchronizationContext()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MobileAds_SetUnityMainThreadSynchronizationContext_mC7C80D814660CF6742100D36D03F879120BBE27F (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		SynchronizationContext_tCDB842BBE53B050802CBBB59C6E6DC45B5B06DC0* L_0;
		L_0 = SynchronizationContext_get_Current_m8DE6D3020745B7955249A2470A23EC0ECBB02A82(NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____synchronizationContext_2 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____synchronizationContext_2), (void*)L_0);
		Thread_t0A773B9DE873D2DCAA7D229EAB36757B500E207F* L_1;
		L_1 = Thread_get_CurrentThread_m6D4719F4993DB9200490531FF02D4076FF9CA9BD(NULL);
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Thread_get_ManagedThreadId_m74ACB74A574EE535C2B00B7D64F203A62E796B05(L_1, NULL);
		((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->____unityMainThreadId_3 = L_2;
		return;
	}
}
// GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.Api.MobileAds::GetMobileAdsClient()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MobileAds_GetMobileAdsClient_m1A2ADAC0C61D673C13E8B06068EDFBACC58FCD14 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0;
		L_0 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_0);
		RuntimeObject* L_1;
		L_1 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(6 /* GoogleMobileAds.Common.IMobileAdsClient GoogleMobileAds.IClientFactory::MobileAdsInstance() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey0__ctor_m6DFCA2A06E9120467C5E1B941B84435F48BC9D74 (U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0::<>m__0(GoogleMobileAds.Common.IInitializationStatusClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey0_U3CU3Em__0_m8D6DA3278704BF784A5424EF89AAAC90A79F4414 (U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* __this, RuntimeObject* ___0_initStatusClient, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* V_0 = NULL;
	{
		U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* L_0 = (U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386*)il2cpp_codegen_object_new(U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CInitializeU3Ec__AnonStorey1__ctor_m8E0E7248D227077736A33E327508E616F92FCCF3(L_0, NULL);
		V_0 = L_0;
		U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* L_2 = V_0;
		RuntimeObject* L_3 = ___0_initStatusClient;
		NullCheck(L_2);
		L_2->___initStatusClient_0 = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___initStatusClient_0), (void*)L_3);
		U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey1__ctor_m8E0E7248D227077736A33E327508E616F92FCCF3 (U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds/<Initialize>c__AnonStorey0/<Initialize>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CInitializeU3Ec__AnonStorey1_U3CU3Em__0_m083F4C7632BD37BCDBEB2AC535DED3635645FD24 (U3CInitializeU3Ec__AnonStorey1_t7ECFBA0A24C1ADAF491218897F06CE0674F97386* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* L_1 = L_0->___initCompleteAction_0;
		if (!L_1)
		{
			goto IL_002b;
		}
	}
	{
		U3CInitializeU3Ec__AnonStorey0_tB4B8A1B8DEFDACD5B03BCA0CB64B49A6218E956E* L_2 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_2);
		Action_1_t61953204BFE962922BA541482E3ECC9BB03FC8EE* L_3 = L_2->___initCompleteAction_0;
		RuntimeObject* L_4 = __this->___initStatusClient_0;
		InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD* L_5 = (InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD*)il2cpp_codegen_object_new(InitializationStatus_t550D061D96119B8458B9FC389FBA74A1DBEB1DDD_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		InitializationStatus__ctor_m1ECBC8D450C9F80E7D8A8F960722F3ED05AD2F15(L_5, L_4, NULL);
		NullCheck(L_3);
		Action_1_Invoke_m790B395C4CBEA3E5F364964EAC8520817D5C9A92_inline(L_3, L_5, NULL);
	}

IL_002b:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRaiseActionU3Ec__AnonStorey7__ctor_mBA567D38964549F7F8BD83C1B4AD5E28B50AC6E7 (U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.MobileAds/<RaiseAction>c__AnonStorey7::<>m__0(System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRaiseActionU3Ec__AnonStorey7_U3CU3Em__0_mB73A491487F829D0FFA3430C2C0C8B701D1C27DE (U3CRaiseActionU3Ec__AnonStorey7_t0168D77052B36C4AE41A0DCDB591484D2CE3E653* __this, RuntimeObject* ___0_state, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___action_0;
		NullCheck(L_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_0, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.ResponseInfo::.ctor(GoogleMobileAds.Common.IResponseInfoClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ResponseInfo__ctor_m5CDD2ADA5EF001A9269D2A0BACF9390D0B044DFD (ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		RuntimeObject* L_0 = ___0_client;
		__this->____client_0 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_0), (void*)L_0);
		return;
	}
}
// System.String GoogleMobileAds.Api.ResponseInfo::ToString()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ResponseInfo_ToString_m937551A7EFF5F3C279EF1FB99B59B8163C7294C6 (ResponseInfo_t9809BD9030BD343A84738E174B86DBC3658720A0* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____client_0;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(3 /* System.String System.Object::ToString() */, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd::.ctor(GoogleMobileAds.Common.IRewardedAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd__ctor_m25C00373D1190A96D49AE296BB1F51480DAF6340 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		__this->____canShowAd_7 = (bool)1;
		RuntimeObject* L_0 = ___0_client;
		__this->____client_6 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_6), (void*)L_0);
		RewardedAd_RegisterAdEvents_m87AD72161981214787EB23E154912B7576320C44(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_add_OnAdPaid_m292BAC920593D4ABEBEBF931F10D642F37828048 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_remove_OnAdPaid_m16C1A0CCC241E747DFA25B1F197CB27FACA8ED4F (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_add_OnAdFullScreenContentOpened_m90888ED613765C8605BB70CFE0B3C00A840666D6 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_remove_OnAdFullScreenContentOpened_m42DAD1BC8CB02CC59571F4929BCB0F4919BE804B (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_add_OnAdFullScreenContentClosed_m1D80D5D072FDB7CFBB922CFED5992A1A28EB76F7 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_remove_OnAdFullScreenContentClosed_mDB17DFCE47160D1917E5282AA96D9555CB6AAF30 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_add_OnAdFullScreenContentFailed_mFC50DBBC23574A8C6CA195F91259C699559BC0D9 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_remove_OnAdFullScreenContentFailed_m27BFDA16911CF1BA56606ACB9B7FBE810CEFF57B (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedAd,GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_Load_m898A7CFD8143AE42D14C47EC3EAE3CE40B0B5741 (String_t* ___0_adUnitId, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* ___1_request, Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* ___2_adLoadCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_0 = (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey0__ctor_m811853FCD1B2648F4113B428D255007083B1DDEA(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_1 = V_0;
		Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* L_2 = ___2_adLoadCallback;
		NullCheck(L_1);
		L_1->___adLoadCallback_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adLoadCallback_0), (void*)L_2);
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_3 = V_0;
		NullCheck(L_3);
		Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* L_4 = L_3->___adLoadCallback_0;
		if (L_4)
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD, NULL);
		return;
	}

IL_0023:
	{
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_6;
		L_6 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_6);
		RuntimeObject* L_7;
		L_7 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(4 /* GoogleMobileAds.Common.IRewardedAdClient GoogleMobileAds.IClientFactory::BuildRewardedAdClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_6);
		NullCheck(L_5);
		L_5->___client_1 = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->___client_1), (void*)L_7);
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_8 = V_0;
		NullCheck(L_8);
		RuntimeObject* L_9 = L_8->___client_1;
		NullCheck(L_9);
		InterfaceActionInvoker0::Invoke(18 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::CreateRewardedAd() */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_9);
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_10 = V_0;
		NullCheck(L_10);
		RuntimeObject* L_11 = L_10->___client_1;
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_12 = V_0;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_13 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_13, L_12, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8_RuntimeMethod_var), NULL);
		NullCheck(L_11);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_11, L_13);
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_14 = V_0;
		NullCheck(L_14);
		RuntimeObject* L_15 = L_14->___client_1;
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_16 = V_0;
		EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* L_17 = (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*)il2cpp_codegen_object_new(EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		NullCheck(L_17);
		EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9(L_17, L_16, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102_RuntimeMethod_var), NULL);
		NullCheck(L_15);
		InterfaceActionInvoker1< EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_15, L_17);
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_18 = V_0;
		NullCheck(L_18);
		RuntimeObject* L_19 = L_18->___client_1;
		String_t* L_20 = ___0_adUnitId;
		AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* L_21 = ___1_request;
		NullCheck(L_19);
		InterfaceActionInvoker2< String_t*, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* >::Invoke(19 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_19, L_20, L_21);
		return;
	}
}
// System.Boolean GoogleMobileAds.Api.RewardedAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RewardedAd_CanShowAd_m6DFCA02D9029DADED78F05E9590366781FCFFBC6 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	int32_t G_B3_0 = 0;
	{
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		bool L_1 = __this->____canShowAd_7;
		G_B3_0 = ((int32_t)(L_1));
		goto IL_0014;
	}

IL_0013:
	{
		G_B3_0 = 0;
	}

IL_0014:
	{
		return (bool)G_B3_0;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_Show_m63D6659798C98BD34A7F0A1479C0B35E8616A71F (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* ___0_userRewardEarnedCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = RewardedAd_CanShowAd_m6DFCA02D9029DADED78F05E9590366781FCFFBC6(__this, NULL);
		if (!L_0)
		{
			goto IL_0024;
		}
	}
	{
		__this->____canShowAd_7 = (bool)0;
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_1 = ___0_userRewardEarnedCallback;
		__this->____userRewardEarnedCallback_8 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____userRewardEarnedCallback_8), (void*)L_1);
		RuntimeObject* L_2 = __this->____client_6;
		NullCheck(L_2);
		InterfaceActionInvoker0::Invoke(20 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::Show() */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_2);
	}

IL_0024:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::Destroy()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_Destroy_mF51E7A752A2B5C378E94553FE48482D19BDF7319 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(21 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::DestroyRewardedAd() */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_RegisterAdEvents_m87AD72161981214787EB23E154912B7576320C44 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_6;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_1, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(16 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdClicked(System.Action) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_0, L_1);
		RuntimeObject* L_2 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_3 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_3, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(12 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_2, L_3);
		RuntimeObject* L_4 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_5 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_5, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(10 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_4, L_5);
		RuntimeObject* L_6 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_7 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_7, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(14 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_6, L_7);
		RuntimeObject* L_8 = __this->____client_6;
		EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* L_9 = (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E*)il2cpp_codegen_object_new(EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		NullCheck(L_9);
		EventHandler_1__ctor_mEB4BDC01DF31AFC470842A776CABEA9053594C64(L_9, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		InterfaceActionInvoker1< EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* >::Invoke(8 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_8, L_9);
		RuntimeObject* L_10 = __this->____client_6;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_11 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)il2cpp_codegen_object_new(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		NullCheck(L_11);
		Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9(L_11, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA_RuntimeMethod_var), NULL);
		NullCheck(L_10);
		InterfaceActionInvoker1< Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* >::Invoke(4 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_10, L_11);
		RuntimeObject* L_12 = __this->____client_6;
		EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC* L_13 = (EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC*)il2cpp_codegen_object_new(EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mB759CACFB208DA7C79637A2B4F041209D3CEC146(L_13, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566_RuntimeMethod_var), NULL);
		NullCheck(L_12);
		InterfaceActionInvoker1< EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC* >::Invoke(6 /* System.Void GoogleMobileAds.Common.IRewardedAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>) */, IRewardedAdClient_t949F7B5C9EE80E93832663274F37DF3171FCADDE_il2cpp_TypeInfo_var, L_12, L_13);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__0_mBDB96FE2F0D0B64613312D662B0D41C8B7AC947A (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__1_mAD587B14AFE8BD4B01EDF6C3FD4018D2E4DE116B (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__2_mB81B3439AA18349F79F5738F64233CB763E9B7CC (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__3_m42FE908963AFFF13DBAD5ECB22F5A0BEC0DE24E9 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__4_m27416EDD3572F8060F77B0461F80F172FF2E055E (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_sender, AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m0BFAEFFA6CDBF3C353153DAA828D50D74A2801C9(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* L_2 = V_0;
		AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline(L_3, NULL);
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_5 = (AdError_tA3BF7518349885A73447524367693A096B3DD1AE*)il2cpp_codegen_object_new(AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___adError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___adError_0), (void*)L_5);
		U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__5_m42972B2D6E17712FCBF6E1E022875E9B7EC926EA (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_adValue, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3D4D7516A12306F7BDF65F140FEE86BAFC2DC69F(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* L_1 = V_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_2 = ___0_adValue;
		NullCheck(L_1);
		L_1->___adValue_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adValue_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__6_m7633930CBB12361843D2AE6B7957C072CAB8F566 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, RuntimeObject* ___0_sender, Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mCA18C893E9A99D7080F8FF71D49C20D9C5C7BEBE(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* L_1 = V_0;
		Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* L_2 = ___1_args;
		NullCheck(L_1);
		L_1->___args_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___args_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__7_mABE846450E8F394B3B78997B9725549A5BCFC0E2 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdClicked_1;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdClicked_1;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__8()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__8_m526BCA48602DFCDF581927E7EEDC13F2D8A46432 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentClosed_4;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__9()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__9_m7F7275675F95CE5B63EC9D372E59D6A828389FC1 (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentOpened_3;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd::<RegisterAdEvents>m__A()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedAd_U3CRegisterAdEventsU3Em__A_m3DF1400112B787277179F1E09B02C84F923FFE8C (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdImpressionRecorded_2;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdImpressionRecorded_2;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m811853FCD1B2648F4113B428D255007083B1DDEA (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_m4581D6C04844FB1A0AA18AB75480F7289D2B7BE8 (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_m19E293E74E612E80F6D6F1DCF39F804E35F23102 (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* __this, RuntimeObject* ___0_sender, LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* L_0 = (U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey1__ctor_m536F748986C564BA479A6ACEF9EF32F65CCD403D(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* L_2 = V_0;
		LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline(L_3, NULL);
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_5 = (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)il2cpp_codegen_object_new(LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___loadAdError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___loadAdError_0), (void*)L_5);
		U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0::<>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBC573DE6617412CE7E357B55B5B193145DE26ABC (U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* L_0 = __this->___adLoadCallback_0;
		RuntimeObject* L_1 = __this->___client_1;
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_2 = (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09*)il2cpp_codegen_object_new(RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09_il2cpp_TypeInfo_var);
		NullCheck(L_2);
		RewardedAd__ctor_m25C00373D1190A96D49AE296BB1F51480DAF6340(L_2, L_1, NULL);
		NullCheck(L_0);
		Action_2_Invoke_mF6DAD8C7A08A67309D74DB8AF8A22DE7C2B4D8EC_inline(L_0, L_2, (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)NULL, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m536F748986C564BA479A6ACEF9EF32F65CCD403D (U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m1EC3AD4342AF9318AE41E9426F3B5F0F71391F74 (U3CLoadU3Ec__AnonStorey1_tEBED13706F9DA821F8601B069F26E653CB9BAA88* __this, const RuntimeMethod* method) 
{
	{
		U3CLoadU3Ec__AnonStorey0_tE468CDD78DB81EE4DCA4E3649D82B775C9FBC3CA* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_2_tC9845396FA1C64BD50F4252BD4DCF45834121418* L_1 = L_0->___adLoadCallback_0;
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_2 = __this->___loadAdError_0;
		NullCheck(L_1);
		Action_2_Invoke_mF6DAD8C7A08A67309D74DB8AF8A22DE7C2B4D8EC_inline(L_1, (RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09*)NULL, L_2, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m0BFAEFFA6CDBF3C353153DAA828D50D74A2801C9 (U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m48DEF449D149433666CB734E67DDD51416E0DC64 (U3CRegisterAdEventsU3Ec__AnonStorey2_tEB41442900B46CE7A2F25733564A01232E08B1F0* __this, const RuntimeMethod* method) 
{
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = L_0->___OnAdFullScreenContentFailed_5;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = L_2->___OnAdFullScreenContentFailed_5;
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_4 = __this->___adError_0;
		NullCheck(L_3);
		Action_1_Invoke_m1DAFB4BD1D193364D17C992CA9C35E091BEDBA56_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3D4D7516A12306F7BDF65F140FEE86BAFC2DC69F (U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m518FD0F0C488E83549321B07B3A73E39E75569A4 (U3CRegisterAdEventsU3Ec__AnonStorey3_t7F1A4DCB98509B8941B3638E92B84E2CF9281E1E* __this, const RuntimeMethod* method) 
{
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = L_0->___OnAdPaid_0;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = L_2->___OnAdPaid_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_4 = __this->___adValue_0;
		NullCheck(L_3);
		Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mCA18C893E9A99D7080F8FF71D49C20D9C5C7BEBE (U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m674619E42C7991FC207FA28DC577B4946C184960 (U3CRegisterAdEventsU3Ec__AnonStorey4_t040EAEDBE7323F6BB6E2414E9AA189D212A3AA4C* __this, const RuntimeMethod* method) 
{
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_1 = L_0->____userRewardEarnedCallback_8;
		if (!L_1)
		{
			goto IL_0032;
		}
	}
	{
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_3 = L_2->____userRewardEarnedCallback_8;
		Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* L_4 = __this->___args_0;
		NullCheck(L_3);
		Action_1_Invoke_mD16ED15700A9B573C44A2A4CEF4CA51CACA79DCE_inline(L_3, L_4, NULL);
		RewardedAd_t6D9A2A205821D735AB48061D75AC8DC4E3FA5D09* L_5 = __this->___U24this_1;
		NullCheck(L_5);
		L_5->____userRewardEarnedCallback_8 = (Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->____userRewardEarnedCallback_8), (void*)(Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC*)NULL);
	}

IL_0032:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::.ctor(GoogleMobileAds.Common.IRewardedInterstitialAdClient)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd__ctor_mC4C75695E5B5F02F6D36BAEE3D7257CC5281EDE6 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_client, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		__this->____canShowAd_7 = (bool)1;
		RuntimeObject* L_0 = ___0_client;
		__this->____client_6 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____client_6), (void*)L_0);
		RewardedInterstitialAd_RegisterAdEvents_mDDD067D0EA422486BB2BFC590E8D457C5F9EDCD4(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_add_OnAdPaid_m61642D408C52ADA6C593D22FD4EE2DCC4126AB4A (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdPaid(System.Action`1<GoogleMobileAds.Api.AdValue>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_remove_OnAdPaid_mEC3145923EE1CE4BE356F3D4989B7413A2776464 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_0 = NULL;
	Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* V_1 = NULL;
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_0 = __this->___OnAdPaid_0;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = V_0;
		V_1 = L_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF** L_2 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF**)(&__this->___OnAdPaid_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = V_1;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_6 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*>(L_2, ((Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)Castclass((RuntimeObject*)L_5, Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_8 = V_0;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_8) == ((RuntimeObject*)(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_add_OnAdFullScreenContentOpened_m96432FC5B54D014F1238FAEE85F1B7F1CFFA6C33 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentOpened(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_remove_OnAdFullScreenContentOpened_m95AFD690EC9AAF4819A7DF600730BD73AC29A254 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentOpened_3);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_add_OnAdFullScreenContentClosed_m5D29C92E895DBCD5CED5F7CCB96DBFDCFF66D9AA (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentClosed(System.Action)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_remove_OnAdFullScreenContentClosed_m98FD000B830A28446F251FA10804C7B7DD1A4CD7 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* V_1 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = V_0;
		V_1 = L_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_2 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&__this->___OnAdFullScreenContentClosed_4);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = V_1;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*>(L_2, ((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_5, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_8) == ((RuntimeObject*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::add_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_add_OnAdFullScreenContentFailed_m00FC7F022CB2D11C511B6FDC5DA7E59704721191 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::remove_OnAdFullScreenContentFailed(System.Action`1<GoogleMobileAds.Api.AdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_remove_OnAdFullScreenContentFailed_m1530533CFA30FA9724CEEF403B526B7CB27B22AB (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_0 = NULL;
	Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* V_1 = NULL;
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_0 = __this->___OnAdFullScreenContentFailed_5;
		V_0 = L_0;
	}

IL_0007:
	{
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = V_0;
		V_1 = L_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6** L_2 = (Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6**)(&__this->___OnAdFullScreenContentFailed_5);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = V_1;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_4 = ___0_value;
		Delegate_t* L_5;
		L_5 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_3, L_4, NULL);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_6 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_7;
		L_7 = InterlockedCompareExchangeImpl<Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*>(L_2, ((Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)Castclass((RuntimeObject*)L_5, Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6_il2cpp_TypeInfo_var)), L_6);
		V_0 = L_7;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_8 = V_0;
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_9 = V_1;
		if ((!(((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_8) == ((RuntimeObject*)(Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6*)L_9))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Load(System.String,GoogleMobileAds.Api.AdRequest,System.Action`2<GoogleMobileAds.Api.RewardedInterstitialAd,GoogleMobileAds.Api.LoadAdError>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_Load_m680D1B87997D82C62B04B27CFB9D40C1E0E1BC97 (String_t* ___0_adUnitId, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* ___1_request, Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* ___2_adLoadCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_0 = (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey0__ctor_m72D0D4C8650D2D70F35636BEB5903C21DD1ECB85(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_1 = V_0;
		Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* L_2 = ___2_adLoadCallback;
		NullCheck(L_1);
		L_1->___adLoadCallback_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adLoadCallback_0), (void*)L_2);
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_3 = V_0;
		NullCheck(L_3);
		Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* L_4 = L_3->___adLoadCallback_0;
		if (L_4)
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(_stringLiteral6DDEF9D4C8C44B9B579CA38D3F9E6206030FE8CD, NULL);
		return;
	}

IL_0023:
	{
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		RuntimeObject* L_6;
		L_6 = MobileAds_GetClientFactory_mFA1B4391A9B66823B3461742EA7623C6A650395E(NULL);
		NullCheck(L_6);
		RuntimeObject* L_7;
		L_7 = InterfaceFuncInvoker0< RuntimeObject* >::Invoke(5 /* GoogleMobileAds.Common.IRewardedInterstitialAdClient GoogleMobileAds.IClientFactory::BuildRewardedInterstitialAdClient() */, IClientFactory_tB916FBC5880467FBFDF41CE059EF0D7057307252_il2cpp_TypeInfo_var, L_6);
		NullCheck(L_5);
		L_5->___client_1 = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->___client_1), (void*)L_7);
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_8 = V_0;
		NullCheck(L_8);
		RuntimeObject* L_9 = L_8->___client_1;
		NullCheck(L_9);
		InterfaceActionInvoker0::Invoke(18 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::CreateRewardedInterstitialAd() */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_9);
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_10 = V_0;
		NullCheck(L_10);
		RuntimeObject* L_11 = L_10->___client_1;
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_12 = V_0;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_13 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_13, L_12, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E_RuntimeMethod_var), NULL);
		NullCheck(L_11);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(0 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdLoaded(System.EventHandler`1<System.EventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_11, L_13);
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_14 = V_0;
		NullCheck(L_14);
		RuntimeObject* L_15 = L_14->___client_1;
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_16 = V_0;
		EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* L_17 = (EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50*)il2cpp_codegen_object_new(EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50_il2cpp_TypeInfo_var);
		NullCheck(L_17);
		EventHandler_1__ctor_m84AB604E47D9F39CDB26057A3A6202C0E096F5C9(L_17, L_16, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8_RuntimeMethod_var), NULL);
		NullCheck(L_15);
		InterfaceActionInvoker1< EventHandler_1_tCA9CA1333E3C6F3F7AFFC7AB4401ABC3F1049D50* >::Invoke(2 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToLoad(System.EventHandler`1<GoogleMobileAds.Common.LoadAdErrorClientEventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_15, L_17);
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_18 = V_0;
		NullCheck(L_18);
		RuntimeObject* L_19 = L_18->___client_1;
		String_t* L_20 = ___0_adUnitId;
		AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* L_21 = ___1_request;
		NullCheck(L_19);
		InterfaceActionInvoker2< String_t*, AdRequest_tB2AC2CD266CFD5D636C917BC2D938BE96E72BF4D* >::Invoke(19 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::LoadAd(System.String,GoogleMobileAds.Api.AdRequest) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_19, L_20, L_21);
		return;
	}
}
// System.Boolean GoogleMobileAds.Api.RewardedInterstitialAd::CanShowAd()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RewardedInterstitialAd_CanShowAd_m374F4E205E398A48456BEC924CAA5DDD3A443CB0 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	int32_t G_B3_0 = 0;
	{
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_0013;
		}
	}
	{
		bool L_1 = __this->____canShowAd_7;
		G_B3_0 = ((int32_t)(L_1));
		goto IL_0014;
	}

IL_0013:
	{
		G_B3_0 = 0;
	}

IL_0014:
	{
		return (bool)G_B3_0;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Show(System.Action`1<GoogleMobileAds.Api.Reward>)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_Show_m94EC715CC0BB7273E1207CAD0B5D6D0E2248363E (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* ___0_userEarnedRewardCallback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = RewardedInterstitialAd_CanShowAd_m374F4E205E398A48456BEC924CAA5DDD3A443CB0(__this, NULL);
		if (!L_0)
		{
			goto IL_0024;
		}
	}
	{
		__this->____canShowAd_7 = (bool)0;
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_1 = ___0_userEarnedRewardCallback;
		__this->____userRewardEarnedCallback_8 = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____userRewardEarnedCallback_8), (void*)L_1);
		RuntimeObject* L_2 = __this->____client_6;
		NullCheck(L_2);
		InterfaceActionInvoker0::Invoke(20 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::Show() */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_2);
	}

IL_0024:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::Destroy()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_Destroy_m1795BB7A135FE3D473385CD77D9D0FAD9987765E (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->____canShowAd_7 = (bool)0;
		RuntimeObject* L_0 = __this->____client_6;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		RuntimeObject* L_1 = __this->____client_6;
		NullCheck(L_1);
		InterfaceActionInvoker0::Invoke(21 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::DestroyRewardedInterstitialAd() */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_1);
	}

IL_001d:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::RegisterAdEvents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_RegisterAdEvents_mDDD067D0EA422486BB2BFC590E8D457C5F9EDCD4 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____client_6;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_1);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_1, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0_RuntimeMethod_var), NULL);
		NullCheck(L_0);
		InterfaceActionInvoker1< Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* >::Invoke(16 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdClicked(System.Action) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_0, L_1);
		RuntimeObject* L_2 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_3 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_3);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_3, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6_RuntimeMethod_var), NULL);
		NullCheck(L_2);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(12 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidDismissFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_2, L_3);
		RuntimeObject* L_4 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_5 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_5, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8_RuntimeMethod_var), NULL);
		NullCheck(L_4);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(10 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidPresentFullScreenContent(System.EventHandler`1<System.EventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_4, L_5);
		RuntimeObject* L_6 = __this->____client_6;
		EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* L_7 = (EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF*)il2cpp_codegen_object_new(EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		EventHandler_1__ctor_mCB95906E90473A2BABEBC6C286D9268E255A8BC0(L_7, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		InterfaceActionInvoker1< EventHandler_1_tF2D41B212D800E7E7D00F9BDEA817E57153988BF* >::Invoke(14 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdDidRecordImpression(System.EventHandler`1<System.EventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_6, L_7);
		RuntimeObject* L_8 = __this->____client_6;
		EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* L_9 = (EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E*)il2cpp_codegen_object_new(EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E_il2cpp_TypeInfo_var);
		NullCheck(L_9);
		EventHandler_1__ctor_mEB4BDC01DF31AFC470842A776CABEA9053594C64(L_9, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663_RuntimeMethod_var), NULL);
		NullCheck(L_8);
		InterfaceActionInvoker1< EventHandler_1_tE6F594B79408473D4004D4405BB5C5CFBA966E5E* >::Invoke(8 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnAdFailedToPresentFullScreenContent(System.EventHandler`1<GoogleMobileAds.Common.AdErrorClientEventArgs>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_8, L_9);
		RuntimeObject* L_10 = __this->____client_6;
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_11 = (Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF*)il2cpp_codegen_object_new(Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF_il2cpp_TypeInfo_var);
		NullCheck(L_11);
		Action_1__ctor_m8FFEDDFFF020CA7E1BF034AD737AD1FBB0E447E9(L_11, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896_RuntimeMethod_var), NULL);
		NullCheck(L_10);
		InterfaceActionInvoker1< Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* >::Invoke(4 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnPaidEvent(System.Action`1<GoogleMobileAds.Api.AdValue>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_10, L_11);
		RuntimeObject* L_12 = __this->____client_6;
		EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC* L_13 = (EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC*)il2cpp_codegen_object_new(EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC_il2cpp_TypeInfo_var);
		NullCheck(L_13);
		EventHandler_1__ctor_mB759CACFB208DA7C79637A2B4F041209D3CEC146(L_13, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A_RuntimeMethod_var), NULL);
		NullCheck(L_12);
		InterfaceActionInvoker1< EventHandler_1_t2CE28DD464FEA7CA750218186087659B7122DBAC* >::Invoke(6 /* System.Void GoogleMobileAds.Common.IRewardedInterstitialAdClient::add_OnUserEarnedReward(System.EventHandler`1<GoogleMobileAds.Api.Reward>) */, IRewardedInterstitialAdClient_t2FC8FDCB9CA844BC20920D49A995D36DDA10E2D5_il2cpp_TypeInfo_var, L_12, L_13);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__0_m9886A4BC1AD46360532A23EEE8C50BD57F89D1A0 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__1(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__1_m543CC73960970B9313A63C6503E6734B824AB6D6 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__2(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__2_m1B2DC3A87AC052C420FA21CD7087ED63C13CC6D8 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__3(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__3_m25AFB0FBFF3283D64AFC8D8598A7ED8D50565B45 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__4(System.Object,GoogleMobileAds.Common.AdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__4_m5A5F20A6DE1364C4418FC45C08523C9C60127663 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_sender, AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* ___1_error, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m2C583B4F9EFE0317753EB8FDA605DFCB04926F09(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* L_2 = V_0;
		AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* L_3 = ___1_error;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline(L_3, NULL);
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_5 = (AdError_tA3BF7518349885A73447524367693A096B3DD1AE*)il2cpp_codegen_object_new(AdError_tA3BF7518349885A73447524367693A096B3DD1AE_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		AdError__ctor_m15B953B294A80F8AF84ECECC285670B48A188F07(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___adError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___adError_0), (void*)L_5);
		U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__5(GoogleMobileAds.Api.AdValue)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__5_m7189FFA485880D772791054308DAFB9E764B1896 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* ___0_adValue, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3E834E57E2F90B6AFA9BF6D2FA8146EB240AA407(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* L_1 = V_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_2 = ___0_adValue;
		NullCheck(L_1);
		L_1->___adValue_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___adValue_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__6(System.Object,GoogleMobileAds.Api.Reward)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__6_mDA873076A460C83926EE0A2B31152F8B6833280A (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, RuntimeObject* ___0_sender, Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* V_0 = NULL;
	{
		U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* L_0 = (U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD*)il2cpp_codegen_object_new(U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mF94D025E5468729D99B7669F52F788661C5971EA(L_0, NULL);
		V_0 = L_0;
		U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* L_1 = V_0;
		Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* L_2 = ___1_args;
		NullCheck(L_1);
		L_1->___args_0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___args_0), (void*)L_2);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* L_3 = V_0;
		NullCheck(L_3);
		L_3->___U24this_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___U24this_1), (void*)__this);
		U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* L_4 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_5 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_5, L_4, (intptr_t)((void*)U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_5, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__7()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__7_mDEE50CCD5BA5BB53759827D2DCF0EC56B9788056 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdClicked_1;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdClicked_1;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__8()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__8_mE545D8C8B6907A1C842B464D38482C969C1CDFF8 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentClosed_4;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentClosed_4;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__9()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__9_m2EF9C4DAEC1AB7E310DEEEA400DC2B8E95AE94F3 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdFullScreenContentOpened_3;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdFullScreenContentOpened_3;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd::<RegisterAdEvents>m__A()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RewardedInterstitialAd_U3CRegisterAdEventsU3Em__A_m83F066CB15114D6BF8100BE17D85FE5D5BC20351 (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* __this, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->___OnAdImpressionRecorded_2;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = __this->___OnAdImpressionRecorded_2;
		NullCheck(L_1);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(L_1, NULL);
	}

IL_0016:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0__ctor_m72D0D4C8650D2D70F35636BEB5903C21DD1ECB85 (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__0(System.Object,System.EventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__0_mECC5EC99E7320E24831FE0BC35E73A02D534CC9E (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* __this, RuntimeObject* ___0_sender, EventArgs_t37273F03EAC87217701DD431B190FBD84AD7C377* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_0, __this, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_0, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__1(System.Object,GoogleMobileAds.Common.LoadAdErrorClientEventArgs)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__1_mC67F1CCB4AB9D24C87624C3AC7E8672691DA5ED8 (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* __this, RuntimeObject* ___0_sender, LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* ___1_args, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* V_0 = NULL;
	{
		U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* L_0 = (U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9*)il2cpp_codegen_object_new(U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9_il2cpp_TypeInfo_var);
		NullCheck(L_0);
		U3CLoadU3Ec__AnonStorey1__ctor_m2C44857B565DBA1D613F287A035A7E68D2E1EA88(L_0, NULL);
		V_0 = L_0;
		U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* L_1 = V_0;
		NullCheck(L_1);
		L_1->___U3CU3Ef__refU240_1 = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3Ef__refU240_1), (void*)__this);
		U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* L_2 = V_0;
		LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* L_3 = ___1_args;
		NullCheck(L_3);
		RuntimeObject* L_4;
		L_4 = LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline(L_3, NULL);
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_5 = (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)il2cpp_codegen_object_new(LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62_il2cpp_TypeInfo_var);
		NullCheck(L_5);
		LoadAdError__ctor_m616B349928B08AD5C222B3136301B43465DE2A8F(L_5, L_4, NULL);
		NullCheck(L_2);
		L_2->___loadAdError_0 = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_2->___loadAdError_0), (void*)L_5);
		U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* L_6 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		NullCheck(L_7);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_7, L_6, (intptr_t)((void*)U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		MobileAds_RaiseAction_mE70DFA931C5DC14845C02D5B1D0A994FE29370B2(L_7, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0::<>m__2()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey0_U3CU3Em__2_mBFE23EE08DE80A726C8A781AA3B9E9116FEE27CB (U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* L_0 = __this->___adLoadCallback_0;
		RuntimeObject* L_1 = __this->___client_1;
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_2 = (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE*)il2cpp_codegen_object_new(RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE_il2cpp_TypeInfo_var);
		NullCheck(L_2);
		RewardedInterstitialAd__ctor_mC4C75695E5B5F02F6D36BAEE3D7257CC5281EDE6(L_2, L_1, NULL);
		NullCheck(L_0);
		Action_2_Invoke_m0CAC4772DC502A0F8D7DE0FED1691A7CE938A069_inline(L_0, L_2, (LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62*)NULL, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1__ctor_m2C44857B565DBA1D613F287A035A7E68D2E1EA88 (U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<Load>c__AnonStorey0/<Load>c__AnonStorey1::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CLoadU3Ec__AnonStorey1_U3CU3Em__0_m289B43C2132F933F53D4AA7B603EB8D7DC80C851 (U3CLoadU3Ec__AnonStorey1_tB49BA184854164CDD7CBC1F6D3D6EE7A5E14B5F9* __this, const RuntimeMethod* method) 
{
	{
		U3CLoadU3Ec__AnonStorey0_t16E241D6ED7F4DF30CB9F4B87B36F170DE5CF2FF* L_0 = __this->___U3CU3Ef__refU240_1;
		NullCheck(L_0);
		Action_2_t090A3AAEE5E3D7EE1ADD68E695C78C769F95302E* L_1 = L_0->___adLoadCallback_0;
		LoadAdError_t22654493F27C801157B803019F04E69E66CD4A62* L_2 = __this->___loadAdError_0;
		NullCheck(L_1);
		Action_2_Invoke_m0CAC4772DC502A0F8D7DE0FED1691A7CE938A069_inline(L_1, (RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE*)NULL, L_2, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2__ctor_m2C583B4F9EFE0317753EB8FDA605DFCB04926F09 (U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey2::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey2_U3CU3Em__0_m3C9928038A84F013D06EE3BCD7E45200A810FB6E (U3CRegisterAdEventsU3Ec__AnonStorey2_t38C455EBAB8D70007BEB3F2963589BE9BBB48652* __this, const RuntimeMethod* method) 
{
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_1 = L_0->___OnAdFullScreenContentFailed_5;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t069366F3AD155AB4638C995045E0A7F46361C0C6* L_3 = L_2->___OnAdFullScreenContentFailed_5;
		AdError_tA3BF7518349885A73447524367693A096B3DD1AE* L_4 = __this->___adError_0;
		NullCheck(L_3);
		Action_1_Invoke_m1DAFB4BD1D193364D17C992CA9C35E091BEDBA56_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3__ctor_m3E834E57E2F90B6AFA9BF6D2FA8146EB240AA407 (U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey3::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey3_U3CU3Em__0_m3BDDE5CF59A3BDCD7115943D7EB1E51D4EE58775 (U3CRegisterAdEventsU3Ec__AnonStorey3_t1B13034338DA04C2AA1F23E92C54DBCF0929BF87* __this, const RuntimeMethod* method) 
{
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_1 = L_0->___OnAdPaid_0;
		if (!L_1)
		{
			goto IL_0026;
		}
	}
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_t96EA761F7BE52B89F4904338B4DF546EB9F1FAFF* L_3 = L_2->___OnAdPaid_0;
		AdValue_t3D87DDD4D06FAAA212BD2119CA464B4F0FEBBCFD* L_4 = __this->___adValue_0;
		NullCheck(L_3);
		Action_1_Invoke_mD5EF5830EA6DF3B15E6D03D506CC91821D5FDC1E_inline(L_3, L_4, NULL);
	}

IL_0026:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::.ctor()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4__ctor_mF94D025E5468729D99B7669F52F788661C5971EA (U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// System.Void GoogleMobileAds.Api.RewardedInterstitialAd/<RegisterAdEvents>c__AnonStorey4::<>m__0()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CRegisterAdEventsU3Ec__AnonStorey4_U3CU3Em__0_m87AC6E348C2E82696AE9F8E152E483A64F53539B (U3CRegisterAdEventsU3Ec__AnonStorey4_t36B7EF98DD7B8228859E3EE16F1548C91A9154FD* __this, const RuntimeMethod* method) 
{
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_0 = __this->___U24this_1;
		NullCheck(L_0);
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_1 = L_0->____userRewardEarnedCallback_8;
		if (!L_1)
		{
			goto IL_0032;
		}
	}
	{
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_2 = __this->___U24this_1;
		NullCheck(L_2);
		Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC* L_3 = L_2->____userRewardEarnedCallback_8;
		Reward_t0F20A888BB23A15D26BEF02E5C367B31ECB87511* L_4 = __this->___args_0;
		NullCheck(L_3);
		Action_1_Invoke_mD16ED15700A9B573C44A2A4CEF4CA51CACA79DCE_inline(L_3, L_4, NULL);
		RewardedInterstitialAd_t5E0B1087E71C016DFDC50BBFBAA8A2CCEFEA58AE* L_5 = __this->___U24this_1;
		NullCheck(L_5);
		L_5->____userRewardEarnedCallback_8 = (Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&L_5->____userRewardEarnedCallback_8), (void*)(Action_1_tBD6F5D2699E5306B21143A3CFF0438FBBA10A8CC*)NULL);
	}

IL_0032:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* AdErrorClientEventArgs_get_AdErrorClient_mCF46AD61FF8A16FF1EEFE8372CBD6F369F6CF9EF_inline (AdErrorClientEventArgs_t911564D6B3C88F90B85A38EAF26518E4ED46212D* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CAdErrorClientU3Ek__BackingField_1;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl_1)((Il2CppObject*)__this->___method_code_6, reinterpret_cast<RuntimeMethod*>(__this->___method_3));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* LoadAdErrorClientEventArgs_get_LoadAdErrorClient_m0D8BE9C6EABF53908FFC893F742D512A063DFA69_inline (LoadAdErrorClientEventArgs_t620986A0A396DB96DE025A536B77350BCAF9BEF2* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___U3CLoadAdErrorClientU3Ek__BackingField_1;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MobileAds_get_RaiseAdEventsOnUnityMainThread_m42F779998BC24C982D13184FF938C9F78939D5B5_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var);
		bool L_0 = ((MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_StaticFields*)il2cpp_codegen_static_fields_for(MobileAds_tE6A198EF1FBB6C42E5C1062E4B6CD5FB5B5494D5_il2cpp_TypeInfo_var))->___U3CRaiseAdEventsOnUnityMainThreadU3Ek__BackingField_5;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_2_Invoke_m7BFCE0BBCF67689D263059B56A8D79161B698587_gshared_inline (Action_2_t156C43F079E7E68155FCDCD12DC77DD11AEF7E3C* __this, RuntimeObject* ___0_arg1, RuntimeObject* ___1_arg2, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl_1)((Il2CppObject*)__this->___method_code_6, ___0_arg1, ___1_arg2, reinterpret_cast<RuntimeMethod*>(__this->___method_3));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_1_Invoke_mF2422B2DD29F74CE66F791C3F68E288EC7C3DB9E_gshared_inline (Action_1_t6F9EB113EB3F16226AEF811A2744F4111C116C87* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl_1)((Il2CppObject*)__this->___method_code_6, ___0_obj, reinterpret_cast<RuntimeMethod*>(__this->___method_3));
}
