using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class ShiningButtonController : MonoBehaviour
{
    [Header("Animation Settings")]
    public ShineAnimationType animationType = ShineAnimationType.Linear;
    public bool playOnStart = false;
    public bool loopAnimation = false;
    public float animationDuration = 1f;
    public float delayBetweenLoops = 2f;
    
    [Header("Shine Properties")]
    public Color shineColor = Color.white;
    [Range(0f, 5f)]
    public float shineIntensity = 2f;
    [Range(0f, 10f)]
    public float shineSpeed = 2f;
    [Range(0.01f, 1f)]
    public float shineWidth = 0.2f;
    
    [Header("Trigger Settings")]
    public bool triggerOnHover = true;
    public bool triggerOnClick = true;
    
    private Material buttonMaterial;
    private Image buttonImage;
    private Button button;
    private bool isAnimating = false;
    private Coroutine animationCoroutine;
    
    // Shader property IDs for performance
    private static readonly int ShineColorID = Shader.PropertyToID("_ShineColor");
    private static readonly int ShineIntensityID = Shader.PropertyToID("_ShineIntensity");
    private static readonly int ShineSpeedID = Shader.PropertyToID("_ShineSpeed");
    private static readonly int ShineWidthID = Shader.PropertyToID("_ShineWidth");
    private static readonly int AnimationTriggerID = Shader.PropertyToID("_AnimationTrigger");
    private static readonly int AnimationDurationID = Shader.PropertyToID("_AnimationDuration");
    
    public enum ShineAnimationType
    {
        Linear,
        Diagonal,
        Radial,
        Wave,
        Pulse
    }
    
    void Start()
    {
        InitializeComponents();
        SetupMaterial();
        
        if (playOnStart)
        {
            PlayShineAnimation();
        }
    }
    
    void InitializeComponents()
    {
        buttonImage = GetComponent<Image>();
        button = GetComponent<Button>();
        
        if (buttonImage == null)
        {
            Debug.LogError("ShiningButtonController requires an Image component!");
            return;
        }
        
        // Create a new material instance to avoid affecting other buttons
        if (buttonImage.material != null)
        {
            buttonMaterial = new Material(buttonImage.material);
        }
        else
        {
            // Try to find the shining shader
            Shader shiningShader = Shader.Find("UI/ShiningButton");
            if (shiningShader != null)
            {
                buttonMaterial = new Material(shiningShader);
            }
            else
            {
                Debug.LogError("Could not find UI/ShiningButton shader!");
                return;
            }
        }
        
        buttonImage.material = buttonMaterial;
        
        // Setup button events
        if (button != null)
        {
            if (triggerOnClick)
            {
                button.onClick.AddListener(PlayShineAnimation);
            }
        }
    }
    
    void SetupMaterial()
    {
        if (buttonMaterial == null) return;
        
        // Set animation type keyword
        SetAnimationTypeKeyword();
        
        // Set shader properties
        buttonMaterial.SetColor(ShineColorID, shineColor);
        buttonMaterial.SetFloat(ShineIntensityID, shineIntensity);
        buttonMaterial.SetFloat(ShineSpeedID, shineSpeed);
        buttonMaterial.SetFloat(ShineWidthID, shineWidth);
        buttonMaterial.SetFloat(AnimationDurationID, animationDuration);
        buttonMaterial.SetFloat(AnimationTriggerID, 0f);
    }
    
    void SetAnimationTypeKeyword()
    {
        if (buttonMaterial == null) return;
        
        // Disable all keywords first
        buttonMaterial.DisableKeyword("_ANIMATIONTYPE_LINEAR");
        buttonMaterial.DisableKeyword("_ANIMATIONTYPE_DIAGONAL");
        buttonMaterial.DisableKeyword("_ANIMATIONTYPE_RADIAL");
        buttonMaterial.DisableKeyword("_ANIMATIONTYPE_WAVE");
        buttonMaterial.DisableKeyword("_ANIMATIONTYPE_PULSE");
        
        // Enable the selected keyword
        switch (animationType)
        {
            case ShineAnimationType.Linear:
                buttonMaterial.EnableKeyword("_ANIMATIONTYPE_LINEAR");
                break;
            case ShineAnimationType.Diagonal:
                buttonMaterial.EnableKeyword("_ANIMATIONTYPE_DIAGONAL");
                break;
            case ShineAnimationType.Radial:
                buttonMaterial.EnableKeyword("_ANIMATIONTYPE_RADIAL");
                break;
            case ShineAnimationType.Wave:
                buttonMaterial.EnableKeyword("_ANIMATIONTYPE_WAVE");
                break;
            case ShineAnimationType.Pulse:
                buttonMaterial.EnableKeyword("_ANIMATIONTYPE_PULSE");
                break;
        }
    }
    
    public void PlayShineAnimation()
    {
        if (isAnimating && !loopAnimation) return;
        
        if (animationCoroutine != null)
        {
            StopCoroutine(animationCoroutine);
        }
        
        animationCoroutine = StartCoroutine(AnimationCoroutine());
    }
    
    public void StopShineAnimation()
    {
        if (animationCoroutine != null)
        {
            StopCoroutine(animationCoroutine);
            animationCoroutine = null;
        }
        
        isAnimating = false;
        if (buttonMaterial != null)
        {
            buttonMaterial.SetFloat(AnimationTriggerID, 0f);
        }
    }
    
    IEnumerator AnimationCoroutine()
    {
        do
        {
            isAnimating = true;
            
            // Trigger animation
            if (buttonMaterial != null)
            {
                buttonMaterial.SetFloat(AnimationTriggerID, 1f);
            }
            
            // Wait for animation duration
            yield return new WaitForSeconds(animationDuration);
            
            // Stop animation
            if (buttonMaterial != null)
            {
                buttonMaterial.SetFloat(AnimationTriggerID, 0f);
            }
            
            // Wait between loops if looping
            if (loopAnimation)
            {
                yield return new WaitForSeconds(delayBetweenLoops);
            }
            
        } while (loopAnimation);
        
        isAnimating = false;
        animationCoroutine = null;
    }
    
    // Public methods to change animation type at runtime
    public void SetAnimationType(ShineAnimationType newType)
    {
        animationType = newType;
        SetAnimationTypeKeyword();
    }
    
    public void SetShineProperties(Color color, float intensity, float speed, float width)
    {
        shineColor = color;
        shineIntensity = intensity;
        shineSpeed = speed;
        shineWidth = width;
        
        if (buttonMaterial != null)
        {
            buttonMaterial.SetColor(ShineColorID, shineColor);
            buttonMaterial.SetFloat(ShineIntensityID, shineIntensity);
            buttonMaterial.SetFloat(ShineSpeedID, shineSpeed);
            buttonMaterial.SetFloat(ShineWidthID, shineWidth);
        }
    }
    
    // Mouse events for hover trigger
    public void OnPointerEnter()
    {
        if (triggerOnHover)
        {
            PlayShineAnimation();
        }
    }
    
    void OnDestroy()
    {
        if (buttonMaterial != null)
        {
            DestroyImmediate(buttonMaterial);
        }
    }
    
    // Update properties in real-time during development
    void OnValidate()
    {
        if (Application.isPlaying && buttonMaterial != null)
        {
            SetupMaterial();
        }
    }
}
