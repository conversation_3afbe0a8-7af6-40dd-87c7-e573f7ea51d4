using UnityEngine;
public class targetaroundrotatecamera : MonoBehaviour
{
    public GameObject target;
    public float speed = 1.5f;
    public float distance = 10f;
    public float height = 2f;

    private float angle = 0f;

    void Update()
    {
        if (target != null)
        {
            target.GetComponent<Rigidbody>().drag = 10f;
            // Increment angle for continuous rotation
            angle += speed * Time.deltaTime;

            // Calculate position around the target
            float x = target.transform.position.x + Mathf.Cos(angle) * distance;
            float z = target.transform.position.z + Mathf.Sin(angle) * distance;
            float y = target.transform.position.y + height;

            // Set camera position
            transform.position = new Vector3(x, y, z);

            // Make camera look at the target
            transform.LookAt(target.transform);
        }
    }
    void Start()
    {
        target.GetComponent<RCC_CarControllerV3>().engineRunning = false;
    }
}
