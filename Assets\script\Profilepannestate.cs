using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Profilepannestate : MonoBehaviour
{
    public GameObject profilepanel;
    public GameObject mainmenu;

    void Start()
    {
       
        if (PlayerPrefs.GetInt("FirstTime", 1) == 1)
        {
            profilepanel.SetActive(true);
            mainmenu.SetActive(false);
            PlayerPrefs.SetInt("FirstTime", 0);
            PlayerPrefs.Save();
        }
        else
        {
            profilepanel.SetActive(false);
            mainmenu.SetActive(true);
        }
    }
}
