%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d79cb9ecc0d4a6d428ab98a681a33897, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  timeAreaShownRange: {x: -0.22624467, y: 29.193739}
  trackScale: 1
  playRangeEnabled: 0
  timeAreaPlayRange: {x: 3.4028235e+38, y: 3.4028235e+38}
  windowTime: 0
  verticalScroll: 0
  sequencerHeaderWidth: 315
  m_Keys:
  - {fileID: 2485468448683611445, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -8975232157705979008, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -7964361753925554904, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -999149189598164737, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -731152230833771520, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: 475669360099820284, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -5509604007643147559, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: 807493011860425924, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: -5998165894078497006, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  - {fileID: 3725629411297052295, guid: b46bfb2c2ce509948903cc9bf935b4ac, type: 2}
  m_Vals:
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
