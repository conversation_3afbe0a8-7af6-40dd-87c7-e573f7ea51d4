using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

public class grasscollect : MonoBeh<PERSON>our
{
    public GameObject player;
    public GameObject Sound;

    void Start()
    {
        this.GetComponent<DOTweenAnimation>().DOPause();
    }
    

    void OnTriggerStay(Collider other)
    {
        if (other.gameObject.CompareTag("GrassField"))
        {
            if (player.GetComponent<RCC_CarControllerV3>().speed > 10f)
            {
                this.GetComponent<DOTweenAnimation>().DOPlay();
                Sound.SetActive(true);
            }
            else
            {
                this.GetComponent<DOTweenAnimation>().DOPause();
                Sound.SetActive(false);
            }
        }
    }

    void OnTriggerExit(Collider other)
    {
        if (other.gameObject.CompareTag("GrassField"))
        {
            this.GetComponent<DOTweenAnimation>().DOPause();
             Sound.SetActive(false);
        }
    }
}
