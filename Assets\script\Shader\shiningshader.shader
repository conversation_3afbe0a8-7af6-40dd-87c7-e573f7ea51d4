Shader "UI/ShiningButton"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)

        // Shining Properties
        _ShineColor ("Shine Color", Color) = (1,1,1,1)
        _ShineIntensity ("Shine Intensity", Range(0, 5)) = 2
        _ShineSpeed ("Shine Speed", Range(0, 10)) = 2
        _ShineWidth ("Shine Width", Range(0.01, 1)) = 0.2

        // Animation Type Selection
        [KeywordEnum(Linear, Diagonal, Radial, Wave, Pulse)] _AnimationType ("Animation Type", Float) = 0

        // Animation Control
        _AnimationTrigger ("Animation Trigger", Range(0, 1)) = 0
        _AnimationDuration ("Animation Duration", Range(0.1, 5)) = 1

        // Stencil properties for UI
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        [Toggle(UNITY_UI_ALPHACLIP)] _UseUIAlphaClip ("Use Alpha Clip", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            Name "Default"
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #pragma multi_compile_local _ UNITY_UI_CLIP_RECT
            #pragma multi_compile_local _ UNITY_UI_ALPHACLIP
            #pragma multi_compile_local _ANIMATIONTYPE_LINEAR _ANIMATIONTYPE_DIAGONAL _ANIMATIONTYPE_RADIAL _ANIMATIONTYPE_WAVE _ANIMATIONTYPE_PULSE

            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float4 worldPosition : TEXCOORD1;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            fixed4 _Color;
            fixed4 _ShineColor;
            float _ShineIntensity;
            float _ShineSpeed;
            float _ShineWidth;
            float _AnimationTrigger;
            float _AnimationDuration;
            fixed4 _TextureSampleAdd;
            float4 _ClipRect;
            float4 _MainTex_ST;

            v2f vert(appdata_t v)
            {
                v2f OUT;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(OUT);
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);

                OUT.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);

                OUT.color = v.color * _Color;
                return OUT;
            }

            // Animation functions
            float GetLinearShine(float2 uv, float time)
            {
                float progress = fmod(time * _ShineSpeed, _AnimationDuration) / _AnimationDuration;
                float shine = smoothstep(progress - _ShineWidth, progress, uv.x) *
                             smoothstep(progress, progress - _ShineWidth, uv.x);
                return shine;
            }

            float GetDiagonalShine(float2 uv, float time)
            {
                float progress = fmod(time * _ShineSpeed, _AnimationDuration) / _AnimationDuration;
                float diagonal = (uv.x + uv.y) * 0.5;
                float shine = smoothstep(progress - _ShineWidth, progress, diagonal) *
                             smoothstep(progress, progress - _ShineWidth, diagonal);
                return shine;
            }

            float GetRadialShine(float2 uv, float time)
            {
                float progress = fmod(time * _ShineSpeed, _AnimationDuration) / _AnimationDuration;
                float2 center = float2(0.5, 0.5);
                float dist = distance(uv, center);
                float maxDist = 0.707; // sqrt(0.5^2 + 0.5^2)
                float normalizedDist = dist / maxDist;

                float shine = smoothstep(progress - _ShineWidth, progress, normalizedDist) *
                             smoothstep(progress, progress - _ShineWidth, normalizedDist);
                return shine;
            }

            float GetWaveShine(float2 uv, float time)
            {
                float progress = fmod(time * _ShineSpeed, _AnimationDuration) / _AnimationDuration;
                float wave = sin(uv.x * 3.14159 * 4 + time * _ShineSpeed * 2) * 0.1 + uv.x;
                float shine = smoothstep(progress - _ShineWidth, progress, wave) *
                             smoothstep(progress, progress - _ShineWidth, wave);
                return shine;
            }

            float GetPulseShine(float2 uv, float time)
            {
                float progress = fmod(time * _ShineSpeed, _AnimationDuration) / _AnimationDuration;
                float2 center = float2(0.5, 0.5);
                float dist = distance(uv, center);

                // Create pulsing rings
                float pulse = sin(progress * 3.14159 * 2) * 0.5 + 0.5;
                float ring = abs(dist - pulse * 0.7);
                float shine = 1.0 - smoothstep(0, _ShineWidth, ring);
                return shine * pulse;
            }
            fixed4 frag(v2f IN) : SV_Target
            {
                half4 color = (tex2D(_MainTex, IN.texcoord) + _TextureSampleAdd) * IN.color;

                #ifdef UNITY_UI_CLIP_RECT
                color.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
                #endif

                #ifdef UNITY_UI_ALPHACLIP
                clip (color.a - 0.001);
                #endif

                // Calculate shine based on animation type
                float shine = 0;
                float time = _Time.y;

                // Only animate when trigger is active
                if (_AnimationTrigger > 0.5)
                {
                    #ifdef _ANIMATIONTYPE_LINEAR
                    shine = GetLinearShine(IN.texcoord, time);
                    #elif _ANIMATIONTYPE_DIAGONAL
                    shine = GetDiagonalShine(IN.texcoord, time);
                    #elif _ANIMATIONTYPE_RADIAL
                    shine = GetRadialShine(IN.texcoord, time);
                    #elif _ANIMATIONTYPE_WAVE
                    shine = GetWaveShine(IN.texcoord, time);
                    #elif _ANIMATIONTYPE_PULSE
                    shine = GetPulseShine(IN.texcoord, time);
                    #endif
                }

                // Apply shine effect
                float3 shineEffect = _ShineColor.rgb * shine * _ShineIntensity;
                color.rgb += shineEffect;

                return color;
            }
            ENDCG
        }
    }
    FallBack "UI/Default"
}
