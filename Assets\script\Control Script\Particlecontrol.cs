using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Particlecontrol : MonoBehaviour
{
    public ParticleSystem[] Particle;
    public GameObject player;
    void Start()
    {
        foreach (var p in Particle)
        {
            p.Stop();
        }
      
    }
    void OnTriggerStay(Collider other)
    {
        if (other.gameObject.CompareTag("GrassField"))
        {
            if (player.GetComponent<RCC_CarControllerV3>().speed > 10f)
            {
                foreach (var p in Particle)
                {
                    p.Play();
                   
                }
            }
            else
            {
                foreach (var p in Particle)
                {
                    p.Stop();
                 
                }
            }
        }
    }
    void OnTriggerExit(Collider other)
    {
        if (other.gameObject.CompareTag("GrassField"))
        {
            foreach (var p in Particle)
            {
                p.Stop();
                
            }
        }

    }
}
