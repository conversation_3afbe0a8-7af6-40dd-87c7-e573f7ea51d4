{"report": {"modules": [{"name": "AI", "dependencies": [{"name": "NavMeshAgent", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "NavMeshObstacle", "scenes": ["Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "NavMeshProjectSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "NavMeshSettings", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}]}, {"name": "AndroidJNI", "dependencies": []}, {"name": "Animation", "dependencies": [{"name": "Animation", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "AnimationClip", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "Animator", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "AnimatorController", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "AnimatorOverrideController", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Avatar", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "AvatarMask", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Motion", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeAnimatorController", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by Director <PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.director"}]}, {"name": "Audio", "dependencies": [{"name": "AudioBehaviour", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioClip", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioFilter", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AudioHigh<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AudioListener", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioLowPassFilter", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AudioManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AudioMixer", "scenes": ["Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioMixerGroup", "scenes": ["Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioMixerSnapshot", "scenes": ["Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioSource", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "SampleClip", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "Required by Director <PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.director"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "Core", "dependencies": [{"name": "Behaviour", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "BuildSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Camera", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "Component", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Cubemap", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "CubemapArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "DelayedCallManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "EditorExtension", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "Flare", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "GameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GameObject", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "GlobalGameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GraphicsSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "InputManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "LensFlare", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "LevelGameManager", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "Light", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "LightingSettings", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "LightmapSettings", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "LightProbes", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "LODGroup", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "LowerResBlitTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Material", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoBehaviour", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MonoScript", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "NamedObject", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 1, "icon": null}, {"name": "Object", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PlayerSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PreloadData", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "QualitySettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RectTransform", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "ReflectionProbe", "scenes": ["Assets/Game scene/Farming mod.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RenderSettings", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "RenderTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "ResourceManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeInitializeOnLoadManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Shader", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "ShaderNameRegistry", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "SortingGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Sprite", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "SpriteAtlas", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "TagManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "TextAsset", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2D", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2DArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Texture3D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TimeManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "Transform", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by AI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ai"}, {"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by Director <PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.director"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by ParticleSystem Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.particlesystem"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by <PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrain"}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by Vehicles Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vehicles"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Director", "dependencies": [{"name": "PlayableDirector", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}]}, {"name": "IMGUI", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "Input", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "InputLegacy", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "JSONSerialize", "dependencies": [{"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "ParticleSystem", "dependencies": [{"name": "ParticleSystem", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "ParticleSystemRenderer", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}]}, {"name": "Physics", "dependencies": [{"name": "BoxCollider", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "CapsuleCollider", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "Collider", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "ConfigurableJoint", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "Joint", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MeshCollider", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "PhysicMaterial", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "PhysicsManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Rigidbody", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "SphereCollider", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by Vehicles Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vehicles"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Physics2D", "dependencies": [{"name": "Collider2D", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "CompositeCollider2D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Physics2DSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PolygonCollider2D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Rigidbody2D", "scenes": [], "dependencyType": 0, "icon": null}]}, {"name": "RuntimeInitializeOnLoadManagerInitializer", "dependencies": [{"name": "RuntimeInitializeOnLoadManagerInitializer is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}, {"name": "SharedInternals", "dependencies": [{"name": "Required by AI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ai"}, {"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by Core Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.core"}, {"name": "Required by Director <PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.director"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by ParticleSystem Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.particlesystem"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by <PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrain"}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by Vehicles Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vehicles"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Subsystems", "dependencies": [{"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Terrain", "dependencies": [{"name": "Terrain", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "TerrainData", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON>in<PERSON><PERSON>er", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}]}, {"name": "TerrainPhysics", "dependencies": [{"name": "<PERSON>in<PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity"], "dependencyType": 0, "icon": null}]}, {"name": "TextCoreFontEngine", "dependencies": [{"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextCoreTextEngine", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "TextRendering", "dependencies": [{"name": "Font", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}]}, {"name": "TLS", "dependencies": [{"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}]}, {"name": "UI", "dependencies": [{"name": "<PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "CanvasGroup", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/MAINMENU.unity", "Assets/Game scene/spleshscene.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UIElementsNative Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielementsnative"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "UIElements", "dependencies": []}, {"name": "UIElementsNative", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "UnityWebRequest", "dependencies": [{"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "Vehicles", "dependencies": [{"name": "WheelCollider", "scenes": ["Assets/Game scene/Farming mod.unity", "Assets/Game scene/gameplay.unity", "Assets/Game scene/tractortochan.unity"], "dependencyType": 0, "icon": null}]}, {"name": "Video", "dependencies": [{"name": "VideoClip", "scenes": ["Assets/Game scene/spleshscene.unity"], "dependencyType": 0, "icon": null}, {"name": "VideoPlayer", "scenes": [], "dependencyType": 0, "icon": null}]}, {"name": "VR", "dependencies": []}, {"name": "XR", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}]}]}}