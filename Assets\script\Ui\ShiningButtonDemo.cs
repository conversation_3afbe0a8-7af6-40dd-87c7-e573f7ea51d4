using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class ShiningButtonDemo : MonoBehaviour
{
    [Head<PERSON>("Demo Buttons")]
    public List<ShiningButtonController> demoButtons = new List<ShiningButtonController>();
    
    [Header("Control Panel")]
    public Button playAllButton;
    public Button stopAllButton;
    public Slider intensitySlider;
    public Slider speedSlider;
    public Slider widthSlider;
    public Toggle loopToggle;
    
    [Header("Animation Type Buttons")]
    public Button linearButton;
    public Button diagonalButton;
    public Button radialButton;
    public Button waveButton;
    public Button pulseButton;
    
    [Header("Color Presets")]
    public Button goldButton;
    public Button silverButton;
    public Button blueButton;
    public Button greenButton;
    public Button redButton;
    
    private Color[] colorPresets = {
        new Color(1f, 0.84f, 0f, 1f),    // Gold
        new Color(0.75f, 0.75f, 0.75f, 1f), // Silver
        new Color(0.2f, 0.6f, 1f, 1f),   // Blue
        new Color(0.2f, 1f, 0.2f, 1f),   // Green
        new Color(1f, 0.2f, 0.2f, 1f)    // Red
    };
    
    void Start()
    {
        SetupDemoButtons();
        SetupControlPanel();
        SetupAnimationTypeButtons();
        SetupColorPresetButtons();
    }
    
    void SetupDemoButtons()
    {
        // If no buttons are assigned, try to find them automatically
        if (demoButtons.Count == 0)
        {
            ShiningButtonController[] foundButtons = FindObjectsOfType<ShiningButtonController>();
            demoButtons.AddRange(foundButtons);
        }
        
        // Set different animation types for each button
        for (int i = 0; i < demoButtons.Count && i < 5; i++)
        {
            demoButtons[i].SetAnimationType((ShiningButtonController.ShineAnimationType)i);
        }
    }
    
    void SetupControlPanel()
    {
        if (playAllButton != null)
            playAllButton.onClick.AddListener(PlayAllAnimations);
            
        if (stopAllButton != null)
            stopAllButton.onClick.AddListener(StopAllAnimations);
            
        if (intensitySlider != null)
        {
            intensitySlider.minValue = 0f;
            intensitySlider.maxValue = 5f;
            intensitySlider.value = 2f;
            intensitySlider.onValueChanged.AddListener(OnIntensityChanged);
        }
        
        if (speedSlider != null)
        {
            speedSlider.minValue = 0.1f;
            speedSlider.maxValue = 10f;
            speedSlider.value = 2f;
            speedSlider.onValueChanged.AddListener(OnSpeedChanged);
        }
        
        if (widthSlider != null)
        {
            widthSlider.minValue = 0.01f;
            widthSlider.maxValue = 1f;
            widthSlider.value = 0.2f;
            widthSlider.onValueChanged.AddListener(OnWidthChanged);
        }
        
        if (loopToggle != null)
        {
            loopToggle.onValueChanged.AddListener(OnLoopToggled);
        }
    }
    
    void SetupAnimationTypeButtons()
    {
        if (linearButton != null)
            linearButton.onClick.AddListener(() => SetAllAnimationType(ShiningButtonController.ShineAnimationType.Linear));
            
        if (diagonalButton != null)
            diagonalButton.onClick.AddListener(() => SetAllAnimationType(ShiningButtonController.ShineAnimationType.Diagonal));
            
        if (radialButton != null)
            radialButton.onClick.AddListener(() => SetAllAnimationType(ShiningButtonController.ShineAnimationType.Radial));
            
        if (waveButton != null)
            waveButton.onClick.AddListener(() => SetAllAnimationType(ShiningButtonController.ShineAnimationType.Wave));
            
        if (pulseButton != null)
            pulseButton.onClick.AddListener(() => SetAllAnimationType(ShiningButtonController.ShineAnimationType.Pulse));
    }
    
    void SetupColorPresetButtons()
    {
        if (goldButton != null)
            goldButton.onClick.AddListener(() => SetAllShineColor(colorPresets[0]));
            
        if (silverButton != null)
            silverButton.onClick.AddListener(() => SetAllShineColor(colorPresets[1]));
            
        if (blueButton != null)
            blueButton.onClick.AddListener(() => SetAllShineColor(colorPresets[2]));
            
        if (greenButton != null)
            greenButton.onClick.AddListener(() => SetAllShineColor(colorPresets[3]));
            
        if (redButton != null)
            redButton.onClick.AddListener(() => SetAllShineColor(colorPresets[4]));
    }
    
    public void PlayAllAnimations()
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
                button.PlayShineAnimation();
        }
    }
    
    public void StopAllAnimations()
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
                button.StopShineAnimation();
        }
    }
    
    public void SetAllAnimationType(ShiningButtonController.ShineAnimationType animationType)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
                button.SetAnimationType(animationType);
        }
    }
    
    public void SetAllShineColor(Color color)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
            {
                button.SetShineProperties(color, button.shineIntensity, button.shineSpeed, button.shineWidth);
            }
        }
    }
    
    void OnIntensityChanged(float value)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
            {
                button.SetShineProperties(button.shineColor, value, button.shineSpeed, button.shineWidth);
            }
        }
    }
    
    void OnSpeedChanged(float value)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
            {
                button.SetShineProperties(button.shineColor, button.shineIntensity, value, button.shineWidth);
            }
        }
    }
    
    void OnWidthChanged(float value)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
            {
                button.SetShineProperties(button.shineColor, button.shineIntensity, button.shineSpeed, value);
            }
        }
    }
    
    void OnLoopToggled(bool isOn)
    {
        foreach (var button in demoButtons)
        {
            if (button != null)
            {
                button.loopAnimation = isOn;
            }
        }
    }
    
    // Keyboard shortcuts for quick testing
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
            SetAllAnimationType(ShiningButtonController.ShineAnimationType.Linear);
        else if (Input.GetKeyDown(KeyCode.Alpha2))
            SetAllAnimationType(ShiningButtonController.ShineAnimationType.Diagonal);
        else if (Input.GetKeyDown(KeyCode.Alpha3))
            SetAllAnimationType(ShiningButtonController.ShineAnimationType.Radial);
        else if (Input.GetKeyDown(KeyCode.Alpha4))
            SetAllAnimationType(ShiningButtonController.ShineAnimationType.Wave);
        else if (Input.GetKeyDown(KeyCode.Alpha5))
            SetAllAnimationType(ShiningButtonController.ShineAnimationType.Pulse);
        else if (Input.GetKeyDown(KeyCode.Space))
            PlayAllAnimations();
        else if (Input.GetKeyDown(KeyCode.Escape))
            StopAllAnimations();
    }
}
