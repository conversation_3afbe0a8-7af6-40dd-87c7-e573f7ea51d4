using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System;

public class linkattacher : MonoBehaviour
{
    [Head<PERSON>("Active Equipment")]
    [Space(10)]
    public GameObject grasscutter, groundflater, grasspick, saeedmachine, plough;

    [Space(20)]
    [Header("Inactive Equipment")]
    [Space(10)]
    public GameObject grasscutter1, groundflater1, grasspick1, saeedmachine1, plough1;

    [Space(20)]
    [<PERSON><PERSON>("Equipment Check Arrays")]
    [Space(10)]
    public GameObject[] firstcheck, scondcheck;
    public Transform TractorPosition;

    [Space(20)]
    [Header("Fade Screen")]
    [Space(10)]
    public Image fadeScreen; // Assign a UI Image for fade effect
    public float fadeDuration = 0.5f;
    public Transform tractorTransform;
    // Reference to the tractor GameObject
    public Text instructiontext;
    public String[] Triggertext;
    public GameObject instruction;
     void Start()
    {
       
    }
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.CompareTag("Grasscutter"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, grasscutter, grasscutter1));
            instructiontext.text = Triggertext[0];
        }
        else if (other.gameObject.CompareTag("Groundflater"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, groundflater, groundflater1));
            instructiontext.text = Triggertext[3];
        }
        else if (other.gameObject.CompareTag("Grasspicker"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, grasspick, grasspick1));
            instructiontext.text = Triggertext[1];
        }
        else if (other.gameObject.CompareTag("Saeedmachine"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, saeedmachine, saeedmachine1));
            instructiontext.text = Triggertext[4];
        }
        else if (other.gameObject.CompareTag("Plough"))
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            StartCoroutine(HandleEquipmentChange(other, plough, plough1));
            instructiontext.text = Triggertext[2];
        }
    }

    IEnumerator HandleEquipmentChange(Collider other, GameObject activeEquipment, GameObject inactiveEquipment)
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            this.GetComponent<Rigidbody>().drag = 0.01f;
        }

        // Reset tractor position
        if (tractorTransform != null && TractorPosition != null)
        {
            tractorTransform.position = TractorPosition.position;
            tractorTransform.rotation = TractorPosition.rotation;
        }

        // Equipment changes
        activeEquipment.SetActive(true);
        inactiveEquipment.SetActive(false);
        other.gameObject.SetActive(false);

        // Handle check arrays
        foreach (GameObject obj in firstcheck)
        {
            obj.SetActive(false);
        }
        foreach (GameObject obj in scondcheck)
        {
            obj.SetActive(true);
        }

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            RCC_Camera.instance.TPSDistance = 28;
            RCC_Camera.instance.TPSHeight = 6;
            yield return new WaitForSeconds(fadeDuration);
            fadeScreen.gameObject.SetActive(false);
             instruction.SetActive(true);
        }
    }
}
