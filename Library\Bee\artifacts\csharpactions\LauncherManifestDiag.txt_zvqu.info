{"AndroidPlayerBuildProgram.Actions.GenerateManifests+Arguments": {"Configuration": {"TargetSDKVersion": 36, "LauncherManifestTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "Assets/Plugins/Android\\AndroidManifest.xml", "LibraryManifestCustomTemplateUsed": true, "LauncherManifestPath": "launcher\\src\\main\\AndroidManifest.xml", "LibraryManifestPath": "unityLibrary\\src\\main\\AndroidManifest.xml", "TVCompatibility": false, "BannerEnabled": true, "IsGame": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "Generic", "GamepadSupportLevel": "SupportsDPad", "TargetDevices": "AllDevices", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.1, "ForceInternetPermission": false, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": false, "AllowedAutorotateToPortraitUpsideDown": false, "AllowedAutorotateToLandscapeLeft": true, "AllowedAutorotateToLandscapeRight": true, "ARCoreEnabled": false, "SplashScreenScale": "ScaleToFill", "VREnabled": false, "RenderOutsideSafeArea": true, "GraphicsDevices": ["OpenGLES3", "OpenGLES2"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "ChromeOSInputEmulation": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizableWindow": false, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User"}, "Services": {"EnableUnityConnect": false, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": false}, "ProjectPath": "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle", "FeatureChecklist": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Cinemachine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.Analytics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.App-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.Platform-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Android-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Common-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Ump.Android-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Ump-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsNativeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"], "Development": false, "UsingObb": false, "GradleResourcesInformation": {"TargetSDKVersion": 36, "RoundIconsAvailable": false, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": false}, "LauncherManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/LauncherManifestDiag.txt", "LibraryManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/LibraryManifestDiag.txt"}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.GenerateManifests", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.45f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/Android/Manifest/LauncherManifestDiag.txt", "Library/Bee/artifacts/Android/Manifest/LibraryManifestDiag.txt", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml", "D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml"], "inputs": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-firstpass-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Cinemachine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/DOTween-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/DOTweenPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.Analytics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.App-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Firebase.Platform-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Android-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Common-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Ump.Android-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/GoogleMobileAds.Ump-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Timeline-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.DirectorModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.ParticleSystemModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsNativeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt", "C:/Program Files/Unity/Hub/Editor/2021.3.45f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml", "Assets/Plugins/Android/AndroidManifest.xml"], "targetDirectories": []}}